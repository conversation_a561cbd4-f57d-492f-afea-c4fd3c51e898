"""
控制面板组件
提供测试控制和配置功能
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
    QPushButton, QLabel, QSpinBox, QLineEdit, 
    QComboBox, QProgressBar, QTextEdit, QSplitter,
    QFormLayout, QCheckBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon

from src.utils.logger import LoggerMixin


class ControlPanelWidget(QWidget, LoggerMixin):
    """控制面板组件"""
    
    # 信号定义
    start_test_requested = Signal()
    stop_test_requested = Signal()
    pause_test_requested = Signal()
    config_changed = Signal(dict)
    connect_devices_requested = Signal()
    disconnect_devices_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._test_running = False
        self._test_paused = False
        
        self._setup_ui()
        self._setup_connections()
        
        # 状态更新定时器
        self._status_timer = QTimer()
        self._status_timer.timeout.connect(self._update_status)
        self._status_timer.start(1000)  # 每秒更新
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 1. 设备连接控制
        device_group = self._create_device_control_group()
        splitter.addWidget(device_group)
        
        # 2. 测试配置
        config_group = self._create_test_config_group()
        splitter.addWidget(config_group)
        
        # 3. 测试控制
        control_group = self._create_test_control_group()
        splitter.addWidget(control_group)
        
        # 4. 状态显示
        status_group = self._create_status_group()
        splitter.addWidget(status_group)
        
        # 5. 日志显示
        log_group = self._create_log_group()
        splitter.addWidget(log_group)
        
        # 设置分割器比例
        splitter.setSizes([150, 200, 100, 150, 200])
    
    def _create_device_control_group(self):
        """创建设备连接控制组"""
        group = QGroupBox("设备连接")
        layout = QVBoxLayout(group)
        
        # OCT设备配置
        oct_layout = QFormLayout()
        self.oct_ip_edit = QLineEdit("*************")
        self.oct_port_spin = QSpinBox()
        self.oct_port_spin.setRange(1, 65535)
        self.oct_port_spin.setValue(22)
        
        oct_layout.addRow("OCT IP:", self.oct_ip_edit)
        oct_layout.addRow("OCT端口:", self.oct_port_spin)
        
        layout.addLayout(oct_layout)
        
        # NPB设备配置
        npb_layout = QFormLayout()
        self.npb_ips_edit = QLineEdit("*************,*************")
        npb_layout.addRow("NPB IPs:", self.npb_ips_edit)
        
        layout.addLayout(npb_layout)
        
        # 连接按钮
        button_layout = QHBoxLayout()
        self.connect_btn = QPushButton("连接设备")
        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.setEnabled(False)
        
        button_layout.addWidget(self.connect_btn)
        button_layout.addWidget(self.disconnect_btn)
        layout.addLayout(button_layout)
        
        # 连接状态
        self.connection_status_label = QLabel("状态: 未连接")
        layout.addWidget(self.connection_status_label)
        
        return group
    
    def _create_test_config_group(self):
        """创建测试配置组"""
        group = QGroupBox("测试配置")
        layout = QFormLayout(group)
        
        # 测试参数
        self.loop_interval_spin = QSpinBox()
        self.loop_interval_spin.setRange(1, 3600)
        self.loop_interval_spin.setValue(10)
        self.loop_interval_spin.setSuffix(" 秒")
        
        self.stability_timeout_spin = QSpinBox()
        self.stability_timeout_spin.setRange(60, 1800)
        self.stability_timeout_spin.setValue(300)
        self.stability_timeout_spin.setSuffix(" 秒")
        
        self.max_workers_spin = QSpinBox()
        self.max_workers_spin.setRange(1, 16)
        self.max_workers_spin.setValue(4)
        
        # 测试选项
        self.auto_clear_errors_check = QCheckBox("自动清零错误计数")
        self.auto_clear_errors_check.setChecked(True)
        
        self.auto_generate_reports_check = QCheckBox("自动生成报告")
        self.auto_generate_reports_check.setChecked(True)
        
        # 添加到布局
        layout.addRow("循环间隔:", self.loop_interval_spin)
        layout.addRow("稳定性超时:", self.stability_timeout_spin)
        layout.addRow("最大工作线程:", self.max_workers_spin)
        layout.addRow("", self.auto_clear_errors_check)
        layout.addRow("", self.auto_generate_reports_check)
        
        return group
    
    def _create_test_control_group(self):
        """创建测试控制组"""
        group = QGroupBox("测试控制")
        layout = QVBoxLayout(group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始测试")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        self.pause_btn = QPushButton("暂停测试")
        self.pause_btn.setEnabled(False)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group
    
    def _create_status_group(self):
        """创建状态显示组"""
        group = QGroupBox("测试状态")
        layout = QFormLayout(group)
        
        # 状态标签
        self.test_status_label = QLabel("空闲")
        self.test_status_label.setStyleSheet("font-weight: bold; color: #666;")
        
        self.current_loop_label = QLabel("0")
        self.elapsed_time_label = QLabel("00:00:00")
        self.modules_tested_label = QLabel("0/0")
        self.pass_rate_label = QLabel("0%")
        
        layout.addRow("测试状态:", self.test_status_label)
        layout.addRow("当前循环:", self.current_loop_label)
        layout.addRow("运行时间:", self.elapsed_time_label)
        layout.addRow("测试模块:", self.modules_tested_label)
        layout.addRow("通过率:", self.pass_rate_label)
        
        return group
    
    def _create_log_group(self):
        """创建日志显示组"""
        group = QGroupBox("系统日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        
        # 添加一些示例日志
        self.log_text.append("2025-01-11 10:00:00 | INFO | 系统启动完成")
        self.log_text.append("2025-01-11 10:00:01 | INFO | 配置加载成功")
        self.log_text.append("2025-01-11 10:00:02 | INFO | 等待设备连接...")
        
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        
        export_log_btn = QPushButton("导出日志")
        export_log_btn.clicked.connect(self._export_logs)
        
        log_button_layout.addWidget(clear_log_btn)
        log_button_layout.addWidget(export_log_btn)
        log_button_layout.addStretch()
        
        layout.addLayout(log_button_layout)
        
        return group
    
    def _setup_connections(self):
        """设置信号连接"""
        # 设备连接
        self.connect_btn.clicked.connect(self._on_connect_devices)
        self.disconnect_btn.clicked.connect(self._on_disconnect_devices)
        
        # 测试控制
        self.start_btn.clicked.connect(self._on_start_test)
        self.pause_btn.clicked.connect(self._on_pause_test)
        self.stop_btn.clicked.connect(self._on_stop_test)
        
        # 配置变化
        self.loop_interval_spin.valueChanged.connect(self._on_config_changed)
        self.stability_timeout_spin.valueChanged.connect(self._on_config_changed)
        self.max_workers_spin.valueChanged.connect(self._on_config_changed)
    
    def _on_connect_devices(self):
        """连接设备"""
        self.log_info("请求连接设备")
        self.connect_devices_requested.emit()
        
        # 模拟连接过程
        self.connect_btn.setEnabled(False)
        self.connection_status_label.setText("状态: 连接中...")
        
        # 这里应该等待实际的连接结果
        QTimer.singleShot(2000, self._simulate_connection_success)
    
    def _simulate_connection_success(self):
        """模拟连接成功"""
        self.disconnect_btn.setEnabled(True)
        self.start_btn.setEnabled(True)
        self.connection_status_label.setText("状态: 已连接")
        self.connection_status_label.setStyleSheet("color: green; font-weight: bold;")
        self.add_log("设备连接成功")
    
    def _on_disconnect_devices(self):
        """断开设备"""
        self.log_info("请求断开设备")
        self.disconnect_devices_requested.emit()
        
        self.connect_btn.setEnabled(True)
        self.disconnect_btn.setEnabled(False)
        self.start_btn.setEnabled(False)
        self.connection_status_label.setText("状态: 未连接")
        self.connection_status_label.setStyleSheet("color: red;")
        self.add_log("设备已断开")
    
    def _on_start_test(self):
        """开始测试"""
        self.log_info("请求开始测试")
        self.start_test_requested.emit()
        
        self._test_running = True
        self._test_paused = False
        self._update_test_buttons()
        
        self.test_status_label.setText("测试中")
        self.test_status_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        self.progress_bar.setVisible(True)
        self.add_log("测试开始")
    
    def _on_pause_test(self):
        """暂停测试"""
        self.log_info("请求暂停测试")
        self.pause_test_requested.emit()
        
        self._test_paused = not self._test_paused
        self._update_test_buttons()
        
        if self._test_paused:
            self.test_status_label.setText("已暂停")
            self.test_status_label.setStyleSheet("font-weight: bold; color: #FF9800;")
            self.add_log("测试已暂停")
        else:
            self.test_status_label.setText("测试中")
            self.test_status_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
            self.add_log("测试已恢复")
    
    def _on_stop_test(self):
        """停止测试"""
        self.log_info("请求停止测试")
        self.stop_test_requested.emit()
        
        self._test_running = False
        self._test_paused = False
        self._update_test_buttons()
        
        self.test_status_label.setText("已停止")
        self.test_status_label.setStyleSheet("font-weight: bold; color: #f44336;")
        self.progress_bar.setVisible(False)
        self.add_log("测试已停止")
    
    def _update_test_buttons(self):
        """更新测试按钮状态"""
        if self._test_running:
            self.start_btn.setEnabled(False)
            self.pause_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
            
            if self._test_paused:
                self.pause_btn.setText("恢复测试")
            else:
                self.pause_btn.setText("暂停测试")
        else:
            self.start_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.pause_btn.setText("暂停测试")
    
    def _on_config_changed(self):
        """配置变化处理"""
        config = self.get_test_config()
        self.config_changed.emit(config)
        self.log_debug(f"配置已更新: {config}")
    
    def _update_status(self):
        """更新状态显示"""
        if self._test_running and not self._test_paused:
            # 模拟状态更新
            import random
            
            current_loop = random.randint(1, 100)
            self.current_loop_label.setText(str(current_loop))
            
            # 模拟运行时间
            import time
            elapsed = int(time.time()) % 3600  # 简化的时间计算
            hours = elapsed // 3600
            minutes = (elapsed % 3600) // 60
            seconds = elapsed % 60
            self.elapsed_time_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # 模拟通过率
            pass_rate = random.randint(85, 100)
            self.pass_rate_label.setText(f"{pass_rate}%")
            
            # 更新进度条
            self.progress_bar.setValue(random.randint(0, 100))
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"{timestamp} | {level} | {message}"
        self.log_text.append(log_entry)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def _export_logs(self):
        """导出日志"""
        self.log_info("导出系统日志")
        # 这里可以实现日志导出功能
    
    def get_test_config(self) -> dict:
        """获取测试配置"""
        return {
            "oct_ip": self.oct_ip_edit.text(),
            "oct_port": self.oct_port_spin.value(),
            "npb_ips": [ip.strip() for ip in self.npb_ips_edit.text().split(",")],
            "loop_interval": self.loop_interval_spin.value(),
            "stability_timeout": self.stability_timeout_spin.value(),
            "max_workers": self.max_workers_spin.value(),
            "auto_clear_errors": self.auto_clear_errors_check.isChecked(),
            "auto_generate_reports": self.auto_generate_reports_check.isChecked()
        }
    
    def set_test_config(self, config: dict):
        """设置测试配置"""
        self.oct_ip_edit.setText(config.get("oct_ip", ""))
        self.oct_port_spin.setValue(config.get("oct_port", 22))
        self.npb_ips_edit.setText(",".join(config.get("npb_ips", [])))
        self.loop_interval_spin.setValue(config.get("loop_interval", 10))
        self.stability_timeout_spin.setValue(config.get("stability_timeout", 300))
        self.max_workers_spin.setValue(config.get("max_workers", 4))
        self.auto_clear_errors_check.setChecked(config.get("auto_clear_errors", True))
        self.auto_generate_reports_check.setChecked(config.get("auto_generate_reports", True))
