"""
测试编排器
协调整个测试流程，管理组件生命周期和测试状态
"""

from __future__ import annotations
import asyncio
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict

from service.common.my_logger import logger
from front.core.signals import signals
from service.common.const import (
    JUDGE_ALL_MODULE_STABLE_TIME_GAP,
    SLEEP_TIME_AFTER_ALL_MODULE_STABLE,
    RESULT_STR_MAPPER
)
from config import global_store

from service.core.config_manager import ConfigManager, TestConfig
from service.core.connection_manager import ConnectionManager
from service.core.data_collector import DataCollector, DataPoint
from service.core.validation_engine import ValidationEngine, ValidationReport
from service.core.report_generator import ReportGenerator
from service.core.task_manager import TaskManager, TaskPriority


class TestPhase(Enum):
    """测试阶段枚举"""
    INITIALIZING = "initializing"
    CONNECTING = "connecting"
    STABILITY_CHECK = "stability_check"
    CLEARING_ERRORS = "clearing_errors"
    TESTING = "testing"
    GENERATING_REPORTS = "generating_reports"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TestState:
    """测试状态"""
    phase: TestPhase = TestPhase.INITIALIZING
    start_time: Optional[datetime] = None
    current_loop: int = 0
    total_modules: int = 0
    active_modules: List[Union[int, str]] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.active_modules is None:
            self.active_modules = []


class StabilityChecker:
    """稳定性检查器"""
    
    def __init__(self, timeout_seconds: int = 300, check_interval: int = 10):
        self.timeout_seconds = timeout_seconds
        self.check_interval = check_interval
    
    async def check_module_stability(self, data_collector: DataCollector,
                                   npb_connections: Dict[str, str],
                                   slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]]) -> Tuple[bool, List[Dict]]:
        """检查模块稳定性"""
        start_time = time.time()
        unstable_modules = []
        
        while time.time() - start_time < self.timeout_seconds:
            # 采集NPB数据
            npb_result = await data_collector.collect_npb_data(npb_connections, slot_mapping)
            
            if not npb_result.success:
                logger.warning(f"NPB数据采集失败: {npb_result.error_message}")
                await asyncio.sleep(self.check_interval)
                continue
            
            # 检查稳定性
            is_stable, unstable_info = self._analyze_stability(npb_result.data_points, slot_mapping)
            
            if is_stable:
                logger.info("所有模块已达到稳定状态")
                return True, []
            
            unstable_modules = unstable_info
            logger.info(f"检测到 {len(unstable_modules)} 个不稳定模块，继续等待...")
            
            await asyncio.sleep(self.check_interval)
        
        # 超时
        logger.error(f"稳定性检查超时 ({self.timeout_seconds}秒)，仍有 {len(unstable_modules)} 个模块不稳定")
        return False, unstable_modules
    
    def _analyze_stability(self, data_points: List[DataPoint], 
                          slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]]) -> Tuple[bool, List[Dict]]:
        """分析稳定性"""
        unstable_modules = []
        
        # 按模块组织数据
        module_data = defaultdict(dict)
        for dp in data_points:
            if dp.source == "NPB":
                module_data[dp.module_id][dp.parameter] = dp.value
        
        # 检查每个模块的稳定性
        for module_id, ip_port_list in slot_mapping.items():
            for npb_ip, npb_port in ip_port_list:
                send_rate_key = f"sendrate_{npb_port}"
                recv_rate_key = f"rsvrate_{npb_port}"
                
                send_rate = module_data[module_id].get(send_rate_key, 0)
                recv_rate = module_data[module_id].get(recv_rate_key, 0)
                
                if send_rate == 0 or recv_rate == 0:
                    unstable_modules.append({
                        "module_id": module_id,
                        "npb_ip": npb_ip,
                        "npb_port": npb_port,
                        "send_rate": send_rate,
                        "recv_rate": recv_rate
                    })
        
        return len(unstable_modules) == 0, unstable_modules


class TestOrchestrator:
    """测试编排器"""
    
    def __init__(self):
        # 初始化组件
        self.config_manager = ConfigManager()
        self.connection_manager = ConnectionManager(self.config_manager)
        self.task_manager = TaskManager(max_workers=self.config_manager.get_config("max_workers", 4))
        self.data_collector = DataCollector(self.connection_manager, self.task_manager)
        self.validation_engine = ValidationEngine(self.config_manager)
        self.report_generator = ReportGenerator(self.config_manager)
        
        # 测试状态
        self.test_state = TestState()
        self._test_config: Optional[TestConfig] = None
        self._running = False
        
        # 连接信息
        self._oct_connection_id: Optional[str] = None
        self._npb_connections: Dict[str, str] = {}
        self._slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]] = {}
        self._module_sn_mapping: Dict[Union[int, str], str] = {}
        
        # 数据存储
        self._all_data_points: List[DataPoint] = []
        self._validation_reports: Dict[Union[int, str], ValidationReport] = {}
        self._module_pass_status: Dict[Union[int, str], bool] = {}
    
    async def start_test(self, oct_ip: str, oct_ssh_port: int, 
                        config_list: List[Dict], single_loop_time: int,
                        test_start_time: datetime) -> None:
        """启动测试"""
        try:
            logger.info("开始测试流程")
            self._running = True
            
            # 初始化测试配置
            self._test_config = self.config_manager.get_test_config(
                oct_ip, oct_ssh_port, config_list, test_start_time.strftime("%Y-%m-%d %H:%M:%S")
            )
            self._test_config.single_loop_time = single_loop_time
            
            self.test_state.start_time = test_start_time
            self.test_state.phase = TestPhase.INITIALIZING
            
            # 启动组件
            await self._initialize_components()
            
            # 建立连接
            await self._establish_connections()
            
            # 稳定性检查
            await self._perform_stability_check()
            
            # 清零错误计数
            await self._clear_error_counters()
            
            # 执行主测试循环
            await self._execute_main_test_loop()
            
            # 生成报告
            await self._generate_final_reports()
            
            self.test_state.phase = TestPhase.COMPLETED
            logger.info("测试流程完成")
            
        except Exception as e:
            self.test_state.phase = TestPhase.FAILED
            self.test_state.error_message = str(e)
            logger.error(f"测试流程失败: {e}")
            raise
        finally:
            await self._cleanup()
    
    async def _initialize_components(self) -> None:
        """初始化组件"""
        logger.info("初始化组件...")
        
        # 启动任务管理器
        self.task_manager.start()
        
        # 启动连接管理器健康检查
        self.connection_manager.start_health_check()
        
        # 解析配置
        self._parse_test_config()
        
        # 创建UI表格行
        self._create_ui_table_rows()
        
        logger.info("组件初始化完成")
    
    def _parse_test_config(self) -> None:
        """解析测试配置"""
        oct_slot_to_npb_ip_port_tuple_list_mapper = defaultdict(list)
        oct_slot_to_npb_ip_list_mapper = defaultdict(list)
        oct_slot_to_npb_port_list_mapper = defaultdict(list)
        npb_ip_set = set()
        oct_slot_set = set()
        
        for config_row in self._test_config.config_list:
            oct_slot = config_row["oct_slot"]
            npb_ip = config_row["npb_ip"]
            npb_data_port = config_row["npb_data_port"]
            
            oct_slot_set.add(oct_slot)
            npb_ip_set.add(npb_ip)
            
            if npb_ip not in oct_slot_to_npb_ip_list_mapper[oct_slot]:
                oct_slot_to_npb_ip_list_mapper[oct_slot].append(npb_ip)
            
            oct_slot_to_npb_port_list_mapper[oct_slot].append(npb_data_port)
            oct_slot_to_npb_ip_port_tuple_list_mapper[oct_slot].append((npb_ip, npb_data_port))
        
        # 保存解析结果
        self._slot_mapping = dict(oct_slot_to_npb_ip_port_tuple_list_mapper)
        self.test_state.active_modules = sorted(oct_slot_set)
        self.test_state.total_modules = len(oct_slot_set)
        
        # 初始化模块通过状态
        for slot in oct_slot_set:
            self._module_pass_status[slot] = True
    
    def _create_ui_table_rows(self) -> None:
        """创建UI表格行"""
        for oct_slot in self.test_state.active_modules:
            npb_ip_list = []
            npb_port_list = []
            
            for npb_ip, npb_port in self._slot_mapping[oct_slot]:
                if npb_ip not in npb_ip_list:
                    npb_ip_list.append(npb_ip)
                npb_port_list.append(npb_port)
            
            row_dict = {
                "ip": self._test_config.oct_ip,
                "port": self._test_config.oct_ssh_port,
                "slot": oct_slot,
                "npb_ip": "-".join(npb_ip_list),
                "npb_data_port": "-".join(npb_port_list),
            }
            signals.table_create_single_row_signal.emit(row_dict)
    
    async def _establish_connections(self) -> None:
        """建立连接"""
        logger.info("建立设备连接...")
        self.test_state.phase = TestPhase.CONNECTING
        
        # 创建OCT连接
        self._oct_connection_id = self.connection_manager.create_oct_connection(
            self._test_config.oct_ip,
            self._test_config.oct_ssh_port,
            self.test_state.active_modules
        )
        
        # 创建NPB连接
        npb_ip_set = set()
        for ip_port_list in self._slot_mapping.values():
            for npb_ip, _ in ip_port_list:
                npb_ip_set.add(npb_ip)
        
        self._npb_connections = self.connection_manager.create_npb_connections(npb_ip_set)
        
        # 读取模块SN信息
        await self._read_module_info()
        
        logger.info("设备连接建立完成")
    
    async def _read_module_info(self) -> None:
        """读取模块信息"""
        logger.info("读取模块信息...")
        
        # 采集OCT数据获取SN
        oct_result = await self.data_collector.collect_oct_data(
            self._oct_connection_id, self.test_state.active_modules
        )
        
        if not oct_result.success:
            raise Exception(f"读取模块信息失败: {oct_result.error_message}")
        
        # 提取SN信息
        for data_point in oct_result.data_points:
            if "sn" in data_point.metadata:
                self._module_sn_mapping[data_point.module_id] = data_point.metadata["sn"]
                
                # 更新UI显示SN
                signals.table_update_single_row_signal.emit(
                    (data_point.module_id,), {"sn": data_point.metadata["sn"]}
                )
        
        logger.info(f"读取到 {len(self._module_sn_mapping)} 个模块的SN信息")
    
    async def _perform_stability_check(self) -> None:
        """执行稳定性检查"""
        logger.info("开始稳定性检查...")
        self.test_state.phase = TestPhase.STABILITY_CHECK
        
        stability_checker = StabilityChecker(
            timeout_seconds=self._test_config.stability_check_timeout,
            check_interval=self._test_config.stability_check_interval
        )
        
        is_stable, unstable_modules = await stability_checker.check_module_stability(
            self.data_collector, self._npb_connections, self._slot_mapping
        )
        
        if not is_stable:
            unstable_info = []
            for module_info in unstable_modules:
                info_str = (f"模块{module_info['module_id']}: "
                          f"NPB {module_info['npb_ip']}:{module_info['npb_port']} "
                          f"发送速率={module_info['send_rate']}, "
                          f"接收速率={module_info['recv_rate']}")
                unstable_info.append(info_str)
            
            raise TimeoutError(f"稳定性检查超时，不稳定模块: {'; '.join(unstable_info)}")
        
        logger.info("稳定性检查通过，等待清零前延时...")
        await asyncio.sleep(self._test_config.post_stability_wait_time)
    
    async def _clear_error_counters(self) -> None:
        """清零错误计数器"""
        logger.info("清零错误计数器...")
        self.test_state.phase = TestPhase.CLEARING_ERRORS
        
        # 为每个NPB端口创建清零任务
        clear_tasks = []
        for slot, ip_port_list in self._slot_mapping.items():
            for npb_ip, npb_port in ip_port_list:
                connection_id = self._npb_connections.get(npb_ip)
                if connection_id:
                    task_id = self.task_manager.create_task(
                        name=f"clear_errors_{npb_ip}_{npb_port}",
                        func=self._clear_single_npb_error,
                        connection_id=connection_id,
                        npb_port=npb_port,
                        priority=TaskPriority.HIGH
                    )
                    clear_tasks.append(task_id)
        
        # 等待所有清零任务完成
        if clear_tasks:
            results = self.task_manager.wait_for_all_tasks(clear_tasks, timeout=60.0)
            failed_tasks = [task_id for task_id, result in results.items() 
                          if not result.status.value == "completed"]
            
            if failed_tasks:
                logger.warning(f"部分清零任务失败: {failed_tasks}")
        
        logger.info("错误计数器清零完成")
    
    def _clear_single_npb_error(self, connection_id: str, npb_port: str) -> None:
        """清零单个NPB端口的错误计数"""
        client = self.connection_manager.get_connection(connection_id)
        if client:
            client.clear_error_nums(npb_port)
    
    async def _execute_main_test_loop(self) -> None:
        """执行主测试循环"""
        logger.info("开始主测试循环...")
        self.test_state.phase = TestPhase.TESTING
        
        loop_interval = timedelta(seconds=self._test_config.single_loop_time)
        
        while self._running and global_store.get_context("test_running", True):
            loop_start_time = datetime.now()
            self.test_state.current_loop += 1
            
            try:
                # 采集所有数据
                collection_results = await self.data_collector.collect_all_data(
                    self._oct_connection_id,
                    self.test_state.active_modules,
                    self._npb_connections,
                    self._slot_mapping
                )
                
                # 合并数据点
                current_data_points = []
                for result in collection_results.values():
                    if result.success:
                        current_data_points.extend(result.data_points)
                
                # 验证数据
                validation_reports = self.validation_engine.validate_all_modules(current_data_points)
                
                # 更新状态和UI
                await self._update_test_status(current_data_points, validation_reports)
                
                # 生成实时报告
                await self._generate_realtime_reports(current_data_points, validation_reports)
                
                # 保存数据
                self._all_data_points.extend(current_data_points)
                self._validation_reports.update(validation_reports)
                
            except Exception as e:
                logger.error(f"测试循环第 {self.test_state.current_loop} 次出错: {e}")
            
            # 控制循环间隔
            loop_end_time = datetime.now()
            elapsed_time = loop_end_time - loop_start_time
            sleep_time = loop_interval - elapsed_time
            
            if sleep_time.total_seconds() > 0:
                await asyncio.sleep(sleep_time.total_seconds())
        
        logger.info(f"主测试循环结束，共执行 {self.test_state.current_loop} 次")
    
    async def _update_test_status(self, data_points: List[DataPoint], 
                                validation_reports: Dict[Union[int, str], ValidationReport]) -> None:
        """更新测试状态和UI"""
        for module_id, report in validation_reports.items():
            # 更新模块通过状态
            if not report.is_pass:
                self._module_pass_status[module_id] = False
                # 更新UI行颜色
                signals.table_change_row_color_signal.emit((module_id,))
            
            # 更新UI数据
            module_data = {}
            for dp in data_points:
                if dp.module_id == module_id:
                    module_data[dp.parameter] = dp.value
            
            if module_data:
                signals.table_update_single_row_signal.emit((module_id,), module_data)
    
    async def _generate_realtime_reports(self, data_points: List[DataPoint],
                                       validation_reports: Dict[Union[int, str], ValidationReport]) -> None:
        """生成实时报告"""
        # 每10次循环生成一次报告
        if self.test_state.current_loop % 10 == 0:
            try:
                report_paths = self.report_generator.generate_module_reports(
                    self.test_state.start_time,
                    data_points,
                    validation_reports,
                    self._module_sn_mapping
                )
                logger.debug(f"生成实时报告: {len(report_paths)} 个文件")
            except Exception as e:
                logger.error(f"生成实时报告失败: {e}")
    
    async def _generate_final_reports(self) -> None:
        """生成最终报告"""
        logger.info("生成最终报告...")
        self.test_state.phase = TestPhase.GENERATING_REPORTS
        
        try:
            # 生成模块报告
            module_report_paths = self.report_generator.generate_module_reports(
                self.test_state.start_time,
                self._all_data_points,
                self._validation_reports,
                self._module_sn_mapping
            )
            
            # 生成汇总报告
            summary_report_path = self.report_generator.generate_summary_report(
                self.test_state.start_time,
                self._validation_reports,
                self._module_sn_mapping
            )
            
            logger.info(f"最终报告生成完成: {len(module_report_paths)} 个模块报告 + 1 个汇总报告")
            
        except Exception as e:
            logger.error(f"生成最终报告失败: {e}")
            raise
    
    async def _cleanup(self) -> None:
        """清理资源"""
        logger.info("清理资源...")
        
        try:
            # 停止任务管理器
            self.task_manager.stop()
            
            # 关闭所有连接
            self.connection_manager.close_all_connections()
            
            # 清理数据缓存
            self.data_collector.clear_cache()
            
        except Exception as e:
            logger.error(f"资源清理出错: {e}")
    
    def stop_test(self) -> None:
        """停止测试"""
        logger.info("停止测试...")
        self._running = False
        global_store.set_context("test_running", False)
        
        if self.test_state.phase not in [TestPhase.COMPLETED, TestPhase.FAILED]:
            self.test_state.phase = TestPhase.CANCELLED
    
    def get_test_status(self) -> Dict[str, Any]:
        """获取测试状态"""
        return {
            "phase": self.test_state.phase.value,
            "start_time": self.test_state.start_time.isoformat() if self.test_state.start_time else None,
            "current_loop": self.test_state.current_loop,
            "total_modules": self.test_state.total_modules,
            "active_modules": self.test_state.active_modules,
            "module_pass_status": self._module_pass_status,
            "error_message": self.test_state.error_message,
            "running": self._running
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "test_status": self.get_test_status(),
            "connection_status": self.connection_manager.get_connection_status(),
            "data_collector_stats": self.data_collector.get_statistics(),
            "validation_stats": self.validation_engine.get_validation_statistics(),
            "report_stats": self.report_generator.get_report_statistics(),
            "task_manager_stats": self.task_manager.get_statistics()
        }
