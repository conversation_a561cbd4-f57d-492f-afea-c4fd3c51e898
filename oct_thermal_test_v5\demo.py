#!/usr/bin/env python3
"""
OCT Thermal Test V5 演示程序
展示新架构的基本功能
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def demo_config_system():
    """演示配置系统"""
    print("=" * 60)
    print("1. 配置系统演示")
    print("=" * 60)
    
    try:
        from config import get_settings
        
        settings = get_settings()
        print(f"✅ 应用名称: {settings.app_name}")
        print(f"✅ 应用版本: {settings.app_version}")
        print(f"✅ OCT设备主机: {settings.oct_device.host}")
        print(f"✅ OCT设备端口: {settings.oct_device.port}")
        print(f"✅ NPB设备列表: {settings.npb_device.hosts}")
        print(f"✅ 测试循环间隔: {settings.test.loop_interval}秒")
        print(f"✅ 最大工作线程: {settings.test.max_workers}")
        print(f"✅ 验证规则数量: {len(settings.test.validation_rules)}")
        
        # 显示验证规则
        print("\n验证规则:")
        for rule in settings.test.validation_rules[:3]:  # 显示前3个
            print(f"  - {rule.name}: {rule.min_value} ~ {rule.max_value} {rule.unit or ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统错误: {e}")
        return False


def demo_logger_system():
    """演示日志系统"""
    print("\n" + "=" * 60)
    print("2. 日志系统演示")
    print("=" * 60)
    
    try:
        from src.utils.logger import get_logger, setup_logger
        from config import get_settings
        
        # 设置日志
        settings = get_settings()
        setup_logger(settings.logging)
        
        # 获取日志记录器
        logger = get_logger("demo")
        
        print("✅ 日志系统初始化成功")
        
        # 测试不同级别的日志
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.debug("这是一条调试日志")
        
        print("✅ 日志记录测试完成")
        print(f"✅ 日志文件路径: {settings.logging.file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志系统错误: {e}")
        return False


def demo_core_components():
    """演示核心组件"""
    print("\n" + "=" * 60)
    print("3. 核心组件演示")
    print("=" * 60)
    
    try:
        from config import get_settings
        from src.core.device_manager import DeviceManager
        from src.core.test_engine import TestEngine
        
        settings = get_settings()
        
        # 创建设备管理器
        device_manager = DeviceManager(settings)
        print("✅ 设备管理器创建成功")
        
        # 创建测试引擎
        test_engine = TestEngine(settings, device_manager)
        print("✅ 测试引擎创建成功")
        
        # 测试基本功能
        print(f"✅ 测试引擎运行状态: {test_engine.is_running()}")
        
        # 模拟启动和停止
        test_engine.start_test()
        print(f"✅ 启动后运行状态: {test_engine.is_running()}")
        
        test_engine.stop_test()
        print(f"✅ 停止后运行状态: {test_engine.is_running()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心组件错误: {e}")
        return False


def demo_project_structure():
    """演示项目结构"""
    print("\n" + "=" * 60)
    print("4. 项目结构演示")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    
    # 检查关键目录和文件
    key_paths = [
        "config/default.json",
        "src/app/application.py",
        "src/core/test_engine.py",
        "src/core/device_manager.py",
        "src/utils/logger.py",
        "src/ui/main_window.py",
        "requirements.txt",
        "pyproject.toml",
        "README.md"
    ]
    
    print("关键文件检查:")
    all_exist = True
    for path in key_paths:
        full_path = project_root / path
        if full_path.exists():
            print(f"  ✅ {path}")
        else:
            print(f"  ❌ {path} (缺失)")
            all_exist = False
    
    # 显示目录结构
    print(f"\n项目根目录: {project_root}")
    print("目录结构:")
    
    def show_tree(path, prefix="", max_depth=2, current_depth=0):
        if current_depth >= max_depth:
            return
        
        items = sorted([p for p in path.iterdir() if not p.name.startswith('.')])
        for i, item in enumerate(items):
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item.name}")
            
            if item.is_dir() and current_depth < max_depth - 1:
                next_prefix = prefix + ("    " if is_last else "│   ")
                show_tree(item, next_prefix, max_depth, current_depth + 1)
    
    show_tree(project_root)
    
    return all_exist


def demo_architecture_benefits():
    """演示架构优势"""
    print("\n" + "=" * 60)
    print("5. 架构优势演示")
    print("=" * 60)
    
    benefits = [
        ("模块化设计", "每个组件职责单一，易于维护和测试"),
        ("类型安全", "使用Pydantic进行配置验证，减少运行时错误"),
        ("异步支持", "支持异步IO操作，提高并发性能"),
        ("配置驱动", "JSON配置文件，灵活的参数配置"),
        ("完善日志", "基于loguru的结构化日志，便于问题诊断"),
        ("PySide6集成", "现代化的GUI框架，响应式界面"),
        ("可扩展性", "插件化架构，易于添加新功能"),
        ("测试友好", "组件化设计，便于单元测试和集成测试")
    ]
    
    print("新架构的主要优势:")
    for i, (title, desc) in enumerate(benefits, 1):
        print(f"  {i}. {title}")
        print(f"     {desc}")
    
    print(f"\n与原有系统对比:")
    print(f"  原系统: 382行单体函数，难以维护")
    print(f"  新系统: 模块化组件，清晰的职责分离")
    print(f"  代码质量: 从难以测试到完全可测试")
    print(f"  并发能力: 从串行处理到异步并发")
    print(f"  错误处理: 从粗糙到细粒度控制")


def main():
    """主演示函数"""
    print("🚀 OCT Thermal Test V5 架构演示")
    print("基于PySide6的现代化温循测试上位机系统")
    
    # 运行各个演示
    results = []
    
    results.append(("配置系统", demo_config_system()))
    results.append(("日志系统", demo_logger_system()))
    results.append(("核心组件", demo_core_components()))
    results.append(("项目结构", demo_project_structure()))
    
    # 显示架构优势
    demo_architecture_benefits()
    
    # 总结
    print("\n" + "=" * 60)
    print("演示结果总结")
    print("=" * 60)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:12} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个模块演示成功")
    
    if passed == len(results):
        print("\n🎉 所有核心模块演示成功！")
        print("新架构已经准备就绪，可以开始具体功能的实现。")
        print("\n下一步建议:")
        print("1. 实现设备接口层 (src/devices/)")
        print("2. 完善UI组件 (src/ui/)")
        print("3. 实现数据处理逻辑 (src/core/)")
        print("4. 添加单元测试 (tests/)")
    else:
        print("\n⚠️ 部分模块需要进一步完善")
    
    return passed == len(results)


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断演示")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n演示过程中发生错误: {e}")
        sys.exit(1)
