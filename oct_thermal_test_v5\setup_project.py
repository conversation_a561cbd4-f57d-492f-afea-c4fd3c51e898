#!/usr/bin/env python3
"""
项目设置脚本
创建必要的目录结构和占位文件
"""

import os
from pathlib import Path


def create_directory_structure():
    """创建项目目录结构"""
    
    directories = [
        # 源代码目录
        "src/ui/widgets",
        "src/ui/dialogs", 
        "src/ui/resources",
        "src/core",
        "src/devices",
        "src/models",
        "src/workers",
        
        # 测试目录
        "tests/test_core",
        "tests/test_devices",
        "tests/test_ui",
        "tests/test_models",
        
        # 文档目录
        "docs",
        
        # 资源目录
        "resources/icons",
        "resources/styles",
        "resources/templates",
        "resources/translations",
        
        # 输出目录
        "output",
        "logs",
        "temp",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
        # 创建 __init__.py 文件（如果是Python包目录）
        if directory.startswith(("src/", "tests/")):
            init_file = Path(directory) / "__init__.py"
            if not init_file.exists():
                init_file.write_text('"""\n模块初始化文件\n"""\n')
    
    print("✅ 目录结构创建完成")


def create_placeholder_files():
    """创建占位文件"""
    
    placeholder_files = {
        # 工具类
        "src/utils/async_utils.py": '''"""
异步工具模块
"""

import asyncio
from typing import Any, Callable, Coroutine


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self):
        self._tasks = []
    
    async def run_task(self, coro: Coroutine) -> Any:
        """运行异步任务"""
        return await coro


def run_async_task(coro: Coroutine) -> Any:
    """运行异步任务的便捷函数"""
    return asyncio.run(coro)
''',
        
        "src/utils/file_utils.py": '''"""
文件工具模块
"""

import shutil
from pathlib import Path
from typing import Union


def ensure_directory(path: Union[str, Path]) -> Path:
    """确保目录存在"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def backup_file(source: Union[str, Path], backup_dir: Union[str, Path]) -> Path:
    """备份文件"""
    source = Path(source)
    backup_dir = Path(backup_dir)
    ensure_directory(backup_dir)
    
    backup_path = backup_dir / source.name
    shutil.copy2(source, backup_path)
    return backup_path


def get_file_size(path: Union[str, Path]) -> int:
    """获取文件大小"""
    return Path(path).stat().st_size
''',
        
        # 核心组件占位
        "src/core/test_engine.py": '''"""
测试引擎
"""

from config.settings import Settings
from src.utils.logger import LoggerMixin


class TestEngine(LoggerMixin):
    """测试引擎主类"""
    
    def __init__(self, settings: Settings, device_manager):
        self.settings = settings
        self.device_manager = device_manager
        self._running = False
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running
    
    def start_test(self):
        """开始测试"""
        self.log_info("测试引擎启动")
        self._running = True
    
    def stop_test(self):
        """停止测试"""
        self.log_info("测试引擎停止")
        self._running = False
''',
        
        "src/core/device_manager.py": '''"""
设备管理器
"""

from config.settings import Settings
from src.utils.logger import LoggerMixin


class DeviceManager(LoggerMixin):
    """设备管理器"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._devices = {}
    
    def connect_all(self):
        """连接所有设备"""
        self.log_info("连接所有设备")
    
    def disconnect_all(self):
        """断开所有设备"""
        self.log_info("断开所有设备")
''',
        
        # UI组件占位
        "src/ui/main_window.py": '''"""
主窗口
"""

from PySide6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import QSettings

from config.settings import Settings
from src.utils.logger import LoggerMixin


class MainWindow(QMainWindow, LoggerMixin):
    """应用程序主窗口"""
    
    def __init__(self, settings: Settings, test_engine, device_manager):
        super().__init__()
        
        self.settings = settings
        self.test_engine = test_engine
        self.device_manager = device_manager
        
        self._setup_ui()
        self._load_layout()
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("OCT Thermal Test V5")
        self.setMinimumSize(800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加占位标签
        label = QLabel("OCT Thermal Test V5\\n\\n项目架构已创建完成！\\n\\n请继续实现具体功能...")
        label.setStyleSheet("font-size: 16px; text-align: center; padding: 50px;")
        layout.addWidget(label)
    
    def _load_layout(self):
        """加载窗口布局"""
        if self.settings.ui.window_size:
            self.resize(*self.settings.ui.window_size)
        
        if self.settings.ui.window_position:
            self.move(*self.settings.ui.window_position)
    
    def save_layout(self):
        """保存窗口布局"""
        self.log_info("保存窗口布局")
''',
        
        # 测试文件
        "tests/test_basic.py": '''"""
基础测试
"""

import pytest
from pathlib import Path


def test_project_structure():
    """测试项目结构"""
    assert Path("config/default.json").exists()
    assert Path("src/app/application.py").exists()
    assert Path("src/utils/logger.py").exists()


def test_config_loading():
    """测试配置加载"""
    from config import get_settings
    
    settings = get_settings()
    assert settings.app_name == "OCT Thermal Test V5"
    assert settings.app_version == "5.0.0"


if __name__ == "__main__":
    pytest.main([__file__])
''',
    }
    
    for file_path, content in placeholder_files.items():
        file_path = Path(file_path)
        if not file_path.exists():
            file_path.write_text(content)
    
    print("✅ 占位文件创建完成")


def create_additional_config_files():
    """创建额外的配置文件"""
    
    # .gitignore
    gitignore_content = """
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Project specific
logs/
output/
temp/
config/user.json
*.log

# OS
.DS_Store
Thumbs.db
"""
    
    Path(".gitignore").write_text(gitignore_content.strip())
    
    # 开发环境配置
    env_example = """
# 开发环境配置示例
# 复制此文件为 .env 并修改相应值

# 调试模式
DEBUG_MODE=false

# 日志级别
LOG_LEVEL=INFO

# 设备连接
OCT_HOST=*************
OCT_PORT=22
NPB_HOSTS=*************,*************

# MES系统
MES_ENABLED=false
MES_BASE_URL=
"""
    
    Path(".env.example").write_text(env_example.strip())
    
    print("✅ 配置文件创建完成")


def main():
    """主函数"""
    print("🚀 开始设置 OCT Thermal Test V5 项目...")
    
    create_directory_structure()
    create_placeholder_files()
    create_additional_config_files()
    
    print("\n🎉 项目设置完成！")
    print("\n下一步:")
    print("1. 安装依赖: pip install -r requirements.txt")
    print("2. 运行测试: python -m pytest tests/")
    print("3. 启动应用: python main.py")
    print("4. 开始开发: 参考 IMPLEMENTATION_PLAN.md")


if __name__ == "__main__":
    main()
