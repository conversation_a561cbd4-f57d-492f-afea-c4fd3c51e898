2025-06-19 09:01:16.469 | INFO     | utils.db_manager:__init__:45 - 初始化数据库管理器，数据库路径: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:01:16.470 | DEBUG    | utils.db_manager:_connect:52 - 正在连接到数据库: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:01:16.586 | INFO     | utils.db_manager:_connect:56 - 数据库连接成功
2025-06-19 09:01:18.927 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:01:19.036 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:01:19.104 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:01:19.107 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:01:19.195 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:01:19.260 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:01:20.759 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:01:20.879 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:01:20.934 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:01:20.936 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:01:21.087 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:01:21.195 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:01:22.312 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:01:22.388 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:01:22.452 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:01:35.719 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:01:35.719 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:01:35.814 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:01:35.823 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:01:35.824 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:01:35.899 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:01:37.081 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:01:37.082 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:01:37.157 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:01:37.159 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:01:37.159 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:01:37.292 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:01:38.422 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:01:38.423 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:01:38.508 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:01:45.264 | INFO     | utils.db_manager:__init__:45 - 初始化数据库管理器，数据库路径: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:01:45.265 | DEBUG    | utils.db_manager:_connect:52 - 正在连接到数据库: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:01:45.266 | INFO     | utils.db_manager:_connect:56 - 数据库连接成功
2025-06-19 09:01:47.450 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:01:47.554 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:01:47.628 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:01:47.629 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:01:47.746 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:01:47.824 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:01:49.167 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:01:49.238 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:01:49.303 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:01:49.304 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:01:49.363 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:01:49.547 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:01:50.659 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:01:51.030 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:01:51.332 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:01:51.832 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:01:51.833 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:01:51.911 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:01:51.916 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:01:51.917 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:01:51.987 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:01:53.182 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:01:53.183 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:01:53.245 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:01:53.246 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:01:53.247 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:01:53.354 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:01:54.494 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:01:54.494 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:01:54.579 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:02:04.602 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:02:04.604 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:02:04.933 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:02:04.935 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:02:04.936 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:02:05.309 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:02:06.594 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:02:06.595 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:02:06.950 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:02:06.952 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:02:06.953 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:02:07.301 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:02:07.878 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:02:07.878 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:02:08.183 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:02:08.185 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:02:08.185 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:02:08.459 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:02:08.592 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:02:08.598 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:02:08.959 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:02:09.766 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:02:09.766 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:02:10.161 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:02:10.166 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:02:10.166 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:02:10.485 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:02:11.644 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:02:11.645 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:02:12.027 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:02:22.262 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:02:22.263 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:02:22.347 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:02:22.348 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:02:22.349 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:02:22.404 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:02:23.565 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:02:23.565 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:02:23.631 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:02:23.633 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:02:23.634 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:02:23.697 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:02:24.835 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:02:24.836 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:02:24.913 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:02:25.301 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:02:25.301 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:02:25.365 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:02:25.367 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:02:25.367 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:02:25.422 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:02:26.602 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:02:26.603 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:02:26.672 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:02:26.675 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:02:26.677 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:02:26.807 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:02:27.916 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:02:27.917 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:02:28.000 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:02:38.196 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:02:38.196 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:02:38.269 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:02:38.270 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:02:38.271 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:02:38.327 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:02:39.497 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:02:39.498 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:02:39.577 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:02:39.579 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:02:39.579 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:02:39.644 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:02:40.807 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:02:40.808 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:02:40.885 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:02:41.293 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:02:41.294 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:02:41.368 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:02:41.370 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:02:41.370 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:02:41.427 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:02:42.607 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:02:42.608 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:02:42.677 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:02:42.679 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:02:42.680 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:02:42.744 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:02:43.890 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:02:43.890 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:02:43.961 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:02:54.154 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:02:54.154 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:02:54.223 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:02:54.225 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:02:54.226 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:02:54.306 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:02:55.470 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:02:55.470 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:02:55.541 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:02:55.543 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:02:55.543 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:02:55.608 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:02:56.742 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:02:56.743 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:02:56.824 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:02:57.250 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:02:57.251 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:02:57.307 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:02:57.309 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:02:57.309 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:02:57.366 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:02:58.537 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:02:58.537 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:02:58.608 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:02:58.610 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:02:58.611 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:02:58.716 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:02:59.866 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:02:59.867 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:02:59.941 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:03:10.091 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:03:10.091 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:03:10.670 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:03:10.672 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:03:10.672 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:03:11.021 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:03:12.215 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:03:12.216 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:03:12.620 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:03:12.622 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:03:12.623 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:03:13.012 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:03:13.233 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:03:13.233 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:03:13.529 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:03:13.531 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:03:13.532 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:03:13.846 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:03:14.189 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:03:14.190 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:03:14.580 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:03:15.028 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:03:15.029 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:03:15.429 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:03:15.431 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:03:15.431 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:03:15.964 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:03:17.195 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:03:17.196 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:03:17.321 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:03:27.877 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:03:27.878 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:03:27.951 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:03:27.953 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:03:27.953 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:03:28.009 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:03:29.207 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:03:29.208 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:03:29.293 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:03:29.295 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:03:29.295 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:03:29.359 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:03:30.495 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:03:30.496 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:03:30.577 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:03:30.607 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:03:30.608 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:03:30.667 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:03:30.669 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:03:30.670 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:03:30.727 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:03:31.915 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:03:31.916 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:03:31.993 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:03:31.995 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:03:31.995 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:03:32.219 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:03:33.366 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:03:33.366 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:03:33.435 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:03:43.884 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:03:43.885 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:03:43.955 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:03:43.957 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:03:43.957 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:03:44.038 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:03:45.216 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:03:45.216 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:03:45.307 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:03:45.309 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:03:45.310 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:03:45.372 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:03:46.499 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:03:46.499 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:03:46.582 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:03:46.756 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:03:46.756 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:03:46.815 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:03:46.817 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:03:46.817 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:03:46.873 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:03:48.069 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:03:48.070 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:03:48.140 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:03:48.141 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:03:48.142 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:03:48.207 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:03:49.387 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:03:49.388 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:03:49.518 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:03:59.871 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:03:59.872 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:03:59.935 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:03:59.937 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:03:59.937 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:04:00.070 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:04:01.263 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:04:01.263 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:04:01.335 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:04:01.338 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:04:01.338 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:04:01.411 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:04:02.652 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:04:02.653 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:04:02.735 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:04:02.820 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:04:02.821 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:04:02.903 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:04:02.905 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:04:02.905 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:04:02.961 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:04:04.139 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:04:04.140 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:04:04.213 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:04:04.215 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:04:04.216 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:04:04.277 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:04:05.435 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:04:05.436 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:04:05.705 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:04:16.013 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:04:16.013 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:04:16.091 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:04:16.092 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:04:16.093 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:04:16.191 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:04:17.389 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:04:17.389 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:04:17.473 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:04:17.475 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:04:17.475 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:04:17.541 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:04:18.720 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:04:18.721 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:04:18.782 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:04:19.004 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:04:19.005 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:04:19.300 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:04:19.302 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:04:19.303 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:04:19.408 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:04:20.591 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:04:20.592 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:04:20.675 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:04:20.676 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:04:20.677 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:04:20.744 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:04:21.934 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:04:21.935 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:04:22.008 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:04:32.086 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:04:32.086 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:04:32.146 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:04:32.148 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:04:32.148 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:04:32.238 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:04:33.417 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:04:33.418 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:04:33.496 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:04:33.498 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:04:33.498 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:04:33.571 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:04:34.717 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:04:34.717 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:04:35.038 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:04:35.306 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:04:35.307 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:04:35.380 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:04:35.381 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:04:35.382 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:04:35.437 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:04:36.650 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:04:36.650 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:04:36.721 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:04:36.723 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:04:36.724 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:04:36.798 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:04:37.949 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:04:37.950 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:04:38.006 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:04:48.349 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:04:48.349 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:04:48.427 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:04:48.429 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:04:48.430 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:04:48.491 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:04:49.679 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:04:49.679 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:04:49.751 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:04:49.753 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:04:49.754 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:04:49.809 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:04:50.949 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:04:50.949 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:04:51.027 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:04:51.310 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:04:51.311 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:04:51.652 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:04:51.653 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:04:51.654 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:04:51.795 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:04:53.015 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:04:53.015 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:04:53.361 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:04:53.363 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:04:53.364 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:04:53.561 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:04:54.727 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:04:54.727 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:04:54.811 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:05:04.318 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:05:04.319 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:05:04.516 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:05:04.517 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:05:04.518 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:05:04.741 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:05:05.967 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:05:05.968 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:05:06.615 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:05:06.618 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:05:06.618 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:05:07.323 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:05:08.089 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:05:08.090 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:05:08.476 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:05:08.750 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:05:08.752 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:05:08.752 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:05:08.895 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:05:09.631 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:05:10.334 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:05:10.861 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:05:10.862 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:05:11.400 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:05:11.402 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:05:11.403 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:05:12.395 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:05:13.562 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:05:13.563 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:05:14.397 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:05:23.622 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:05:23.623 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:05:23.695 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:05:23.697 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:05:23.697 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:05:23.804 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:05:25.004 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:05:25.004 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:05:25.131 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:05:25.133 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:05:25.134 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:05:25.400 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:05:26.553 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:05:26.554 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:05:26.680 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:05:27.707 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:05:27.707 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:05:27.779 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:05:27.781 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:05:27.781 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:05:27.847 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:05:29.039 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:05:29.039 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:05:29.113 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:05:29.115 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:05:29.115 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:05:29.180 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:05:30.322 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:05:30.323 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:05:30.404 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:05:39.971 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:05:39.972 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:05:40.050 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:05:40.051 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:05:40.052 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:05:40.116 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:05:41.345 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:05:41.347 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:05:41.427 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:05:41.429 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:05:41.430 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:05:41.637 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:05:42.791 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:05:42.792 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:05:42.876 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:05:43.693 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:05:43.694 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:05:43.767 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:05:43.769 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:05:43.770 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:05:43.867 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:05:45.069 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:05:45.070 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:05:45.193 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:05:45.195 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:05:45.195 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:05:45.260 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:05:46.428 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:05:46.429 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:05:46.501 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:05:56.139 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:05:56.140 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:05:56.213 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:05:56.215 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:05:56.215 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:05:56.272 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:05:57.500 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:05:57.501 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:05:57.579 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:05:57.581 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:05:57.582 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:05:57.656 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:05:58.904 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:05:58.904 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:05:58.990 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:05:59.815 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:05:59.815 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:05:59.921 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:05:59.922 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:05:59.923 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:06:00.113 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:06:01.315 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:06:01.315 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:06:01.440 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:06:01.441 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:06:01.442 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:06:01.506 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:06:02.670 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:06:02.671 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:06:02.753 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:06:12.307 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:06:12.308 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:06:12.744 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:06:12.746 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:06:12.747 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:06:13.169 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:06:14.375 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:06:14.376 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:06:14.794 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:06:14.799 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:06:14.804 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:06:15.362 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:06:16.060 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:06:16.061 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:06:16.652 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:06:16.657 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:06:16.659 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:06:17.032 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:06:17.594 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:06:17.606 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:06:18.303 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:06:20.281 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:06:20.282 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:06:23.517 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:06:23.519 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:06:23.520 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:06:24.580 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:06:26.016 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:06:26.017 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:06:26.869 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:06:31.919 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:06:31.921 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:06:32.217 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:06:32.219 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:06:32.220 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:06:32.624 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:06:33.835 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:06:33.837 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:06:34.597 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:06:34.599 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:06:34.600 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:06:34.851 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:06:36.120 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:06:36.121 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:06:36.428 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:06:42.248 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:06:42.248 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:06:42.740 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:06:42.743 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:06:42.746 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:06:43.420 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:06:44.878 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:06:44.879 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:06:45.642 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:06:45.646 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:06:45.646 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:06:46.122 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:06:47.590 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:06:47.591 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:06:48.461 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:06:49.896 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:06:49.896 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:06:50.288 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:06:50.290 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:06:50.290 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:06:50.572 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:06:52.778 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:06:52.779 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:06:53.357 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:06:53.359 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:06:53.359 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:06:53.715 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:06:55.555 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:06:55.556 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:06:55.932 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:07:03.332 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:07:03.333 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:07:04.108 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:07:04.110 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:07:04.111 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:07:04.560 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:07:05.806 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:07:05.807 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:07:06.717 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:07:06.719 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:07:06.719 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:07:07.227 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:07:08.448 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:07:08.448 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:07:09.195 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:07:09.276 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:07:09.277 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:07:09.678 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:07:09.680 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:07:09.680 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:07:10.262 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:07:11.499 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:07:11.500 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:07:12.104 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:07:12.106 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:07:12.106 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:07:12.797 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:07:13.967 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:07:13.968 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:07:14.439 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:07:22.511 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:07:22.512 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:07:23.159 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:07:23.160 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:07:23.161 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:07:23.775 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:07:25.011 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:07:25.011 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:07:25.955 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:07:25.957 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:07:25.957 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:07:26.827 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:07:27.763 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:07:27.764 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:07:28.044 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:07:28.526 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:07:28.527 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:07:28.527 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:07:28.528 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:07:28.943 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:07:29.467 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:07:30.158 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:07:30.158 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:07:30.502 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:07:30.505 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:07:30.505 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:07:30.880 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:07:32.056 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:07:32.057 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:07:32.360 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:07:42.802 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:07:42.803 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:07:43.247 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:07:43.248 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:07:43.248 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:07:43.422 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:07:44.670 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:07:44.671 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:07:44.854 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:07:44.856 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:07:44.857 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:07:44.990 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:07:45.782 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:07:45.782 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:07:46.099 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:07:46.101 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:07:46.102 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:07:46.168 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:07:46.390 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:07:46.399 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:07:46.890 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:07:47.705 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:07:47.706 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:07:47.933 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:07:47.935 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:07:47.935 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:07:48.149 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:07:49.363 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:07:49.364 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:07:49.532 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:08:00.222 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:08:00.223 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:08:00.361 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:08:00.362 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:08:00.363 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:08:00.444 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:08:01.666 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:08:01.667 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:08:02.069 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:08:02.071 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:08:02.071 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:08:02.202 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:08:02.866 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:08:02.867 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:08:03.378 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:08:03.380 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:08:03.380 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:08:03.396 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:08:03.510 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:08:03.522 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:08:03.620 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:08:04.769 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:08:04.770 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:08:05.455 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:08:05.461 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:08:05.463 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:08:05.905 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:08:07.099 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:08:07.099 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:08:07.456 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:08:16.964 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:08:16.965 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:08:17.451 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:08:17.454 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:08:17.454 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:08:17.882 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:08:19.250 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:08:19.251 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:08:19.466 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:08:19.468 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:08:19.468 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:08:19.679 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:08:20.786 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:08:20.786 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:08:20.902 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:08:20.943 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:08:20.945 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:08:20.945 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:08:21.110 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:08:21.137 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:08:21.316 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:08:22.413 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:08:22.415 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:08:22.895 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:08:22.898 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:08:22.899 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:08:23.060 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:08:24.229 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:08:24.230 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:08:24.776 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:08:34.655 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:08:34.656 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:08:34.796 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:08:34.802 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:08:34.804 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:08:34.957 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:08:36.245 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:08:36.246 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:08:36.381 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:08:36.383 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:08:36.384 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:08:36.531 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:08:37.719 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:08:37.720 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:08:37.899 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:08:38.133 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:08:38.134 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:08:38.283 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:08:38.288 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:08:38.289 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:08:38.457 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:08:39.710 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:08:39.711 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:08:39.868 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:08:39.870 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:08:39.871 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:08:39.998 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:08:41.219 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:08:41.220 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:08:41.358 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:08:51.219 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:08:51.221 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:08:51.363 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:08:51.364 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:08:51.365 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:08:51.486 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:08:52.728 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:08:52.729 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:08:52.986 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:08:52.988 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:08:52.989 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:08:53.120 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:08:54.312 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:08:54.314 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:08:54.470 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:08:54.708 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:08:54.709 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:08:54.828 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:08:54.829 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:08:54.830 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:08:54.979 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:08:56.241 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:08:56.241 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:08:56.362 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:08:56.370 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:08:56.372 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:08:56.472 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:08:57.642 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:08:57.643 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:08:57.804 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:09:07.832 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:09:07.833 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:09:08.224 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:09:08.226 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:09:08.227 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:09:08.590 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:09:09.849 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:09:09.850 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:09:10.318 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:09:10.321 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:09:10.324 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:09:10.742 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:09:11.137 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:09:11.138 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:09:11.602 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:09:11.603 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:09:11.604 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:09:11.948 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:09:12.092 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:09:12.104 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:09:12.450 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:09:13.403 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:09:13.404 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:09:13.767 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:09:13.768 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:09:13.769 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:09:14.350 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:09:15.592 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:09:15.593 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:09:15.926 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:09:25.768 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:09:25.768 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:09:25.913 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:09:25.914 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:09:25.915 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:09:26.068 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:09:27.323 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:09:27.323 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:09:27.638 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:09:27.639 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:09:27.640 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:09:27.806 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:09:29.008 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:09:29.008 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:09:29.147 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:09:29.266 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:09:29.266 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:09:29.405 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:09:29.406 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:09:29.407 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:09:29.530 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:09:30.773 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:09:30.773 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:09:30.914 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:09:30.915 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:09:30.916 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:09:31.065 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:09:32.274 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:09:32.275 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:09:32.391 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:09:42.469 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:09:42.470 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:09:42.634 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:09:42.642 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:09:42.645 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:09:42.800 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:09:44.048 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:09:44.049 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:09:44.211 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:09:44.213 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:09:44.214 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:09:44.369 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:09:45.560 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:09:45.561 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:09:45.735 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:09:45.774 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:09:45.775 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:09:45.969 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:09:45.972 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:09:45.973 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:09:46.153 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:09:47.400 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:09:47.401 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:09:47.553 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:09:47.555 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:09:47.555 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:09:47.704 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:09:48.953 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:09:48.954 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:09:49.070 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:09:59.081 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:09:59.082 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:09:59.248 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:09:59.250 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:09:59.251 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:09:59.324 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:10:00.610 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:10:00.611 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:10:00.689 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:10:00.696 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:10:00.698 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:10:00.848 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:10:02.063 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:10:02.064 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:10:02.148 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:10:02.396 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:10:02.397 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:10:02.466 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:10:02.468 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:10:02.468 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:10:02.532 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:10:03.895 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:10:03.896 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:10:03.973 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:10:03.982 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:10:03.982 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:10:04.074 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:10:05.316 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:10:05.317 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:10:05.701 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:10:15.518 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:10:15.518 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:10:15.953 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:10:15.955 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:10:15.955 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:10:16.331 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:10:17.593 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:10:17.594 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:10:17.922 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:10:17.925 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:10:17.926 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:10:18.186 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:10:19.062 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:10:19.063 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:10:19.358 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:10:19.360 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:10:19.360 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:10:19.406 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:10:19.438 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:10:19.769 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:10:20.054 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:10:21.135 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:10:21.135 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:10:21.483 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:10:21.495 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:10:21.498 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:10:21.897 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:10:23.196 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:10:23.197 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:10:23.640 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:10:33.428 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:10:33.428 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:10:33.526 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:10:33.528 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:10:33.529 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:10:33.600 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:10:34.852 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:10:34.853 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:10:34.983 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:10:34.987 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:10:34.988 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:10:35.329 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:10:36.550 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:10:36.551 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:10:36.723 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:10:36.980 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:10:36.980 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:10:37.068 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:10:37.073 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:10:37.074 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:10:37.169 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:10:38.421 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:10:38.422 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:10:38.511 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:10:38.512 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:10:38.513 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:10:38.586 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:10:39.835 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:10:39.835 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:10:39.965 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:10:50.291 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:10:50.292 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:10:50.372 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:10:50.373 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:10:50.374 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:10:50.473 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:10:51.849 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:10:51.850 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:10:51.941 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:10:51.946 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:10:51.947 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:10:52.023 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:10:53.246 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:10:53.247 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:10:53.340 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:10:53.357 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:10:53.358 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:10:53.423 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:10:53.425 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:10:53.425 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:10:53.491 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:10:54.744 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:10:54.744 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:10:54.823 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:10:54.824 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:10:54.825 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:10:54.890 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:10:56.133 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:10:56.134 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:10:56.315 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:11:06.747 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:11:06.747 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:11:08.492 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:11:08.493 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:11:08.494 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:11:09.110 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:11:09.649 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:11:09.650 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:11:10.204 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:11:10.205 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:11:10.205 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:11:10.509 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:11:10.721 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:11:10.738 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:11:11.145 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:11:11.146 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:11:11.147 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:11:11.545 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:11:12.014 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:11:12.015 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:11:12.442 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:11:12.445 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:11:12.447 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:11:12.769 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:11:13.276 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:11:13.284 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:11:13.646 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:11:14.527 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:11:14.528 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:11:15.197 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:11:27.048 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:11:27.050 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:11:27.301 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:11:27.303 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:11:27.304 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:11:27.542 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:11:28.566 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:11:28.567 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:11:28.776 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:11:28.778 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:11:28.779 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:11:28.852 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:11:29.068 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:11:29.096 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:11:29.355 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:11:29.362 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:11:29.364 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:11:29.608 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:11:30.582 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:11:30.583 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:11:30.814 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:11:30.817 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:11:30.818 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:11:30.898 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:11:30.993 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:11:31.010 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:11:31.253 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:11:32.223 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:11:32.224 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:11:32.585 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:11:44.644 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:11:44.644 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:11:44.818 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:11:44.825 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:11:44.828 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:11:44.990 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:11:46.001 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:11:46.001 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:11:46.240 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:11:46.242 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:11:46.242 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:11:46.274 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:11:46.389 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:11:46.396 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:11:46.556 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:11:46.558 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:11:46.558 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:11:46.724 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:11:47.675 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:11:47.677 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:11:47.873 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:11:47.875 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:11:47.876 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:11:48.018 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:11:48.023 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:11:48.038 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:11:48.368 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:11:49.429 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:11:49.430 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:11:49.682 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:12:01.738 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:12:01.738 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:12:01.852 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:12:01.858 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:12:01.860 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:12:01.979 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:12:03.061 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:12:03.062 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:12:03.221 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:12:03.223 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:12:03.223 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:12:03.272 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:12:03.437 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:12:03.442 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:12:03.603 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:12:03.605 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:12:03.605 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:12:03.762 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:12:04.686 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:12:04.687 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:12:04.796 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:12:04.798 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:12:04.798 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:12:05.019 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:12:05.046 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:12:05.048 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:12:05.513 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:12:06.264 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:12:06.265 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:12:06.656 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:12:18.927 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:12:18.928 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:12:19.059 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:12:19.061 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:12:19.061 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:12:19.125 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:12:20.045 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:12:20.048 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:12:20.139 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:12:20.143 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:12:20.144 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:12:20.317 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:12:20.438 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:12:20.438 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:12:20.592 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:12:20.597 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:12:20.599 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:12:20.674 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:12:21.649 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:12:21.650 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:12:21.744 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:12:21.746 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:12:21.747 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:12:21.858 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:12:21.924 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:12:21.925 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:12:22.039 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:12:23.118 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:12:23.119 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:12:23.205 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:12:35.455 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:12:35.455 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:12:35.543 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:12:35.546 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:12:35.547 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:12:35.672 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:12:36.637 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:12:36.639 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:12:36.706 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:12:36.708 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:12:36.708 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:12:36.771 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:12:36.991 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:12:36.992 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:12:37.139 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:12:37.141 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:12:37.142 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:12:37.215 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:12:38.062 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:12:38.063 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:12:38.143 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:12:38.144 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:12:38.145 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:12:38.315 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:12:38.466 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:12:38.467 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:12:38.607 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:12:39.562 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:12:39.563 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:12:39.649 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:12:52.019 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:12:52.019 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:12:52.110 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:12:52.112 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:12:52.113 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:12:52.177 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:12:53.057 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:12:53.058 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:12:53.137 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:12:53.140 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:12:53.141 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:12:53.244 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:12:53.472 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:12:53.472 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:12:53.554 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:12:53.559 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:12:53.561 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:12:53.685 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:12:54.596 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:12:54.596 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:12:54.687 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:12:54.689 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:12:54.690 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:12:54.812 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:12:54.936 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:12:54.936 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:12:55.062 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:12:56.079 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:12:56.080 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:12:56.255 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:13:08.524 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:13:08.525 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:13:09.043 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:13:09.046 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:13:09.047 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:13:09.673 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:13:09.705 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:13:09.703 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:13:10.392 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:13:10.393 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:13:10.394 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:13:10.741 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:13:11.055 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:13:11.055 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:13:11.450 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:13:11.453 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:13:11.454 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:13:11.808 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:13:12.057 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:13:12.058 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:13:12.677 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:13:12.679 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:13:12.679 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:13:13.061 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:13:13.092 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:13:13.096 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:13:13.717 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:13:14.506 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:13:14.510 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:13:14.842 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:13:27.158 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:13:27.159 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:13:27.239 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:13:27.242 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:13:27.243 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:13:27.322 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:13:28.242 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:13:28.243 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:13:28.325 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:13:28.327 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:13:28.328 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:13:28.523 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:13:28.638 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:13:28.639 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:13:28.756 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:13:28.769 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:13:28.770 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:13:28.872 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:13:29.898 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:13:29.899 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:13:29.981 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:13:29.983 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:13:29.983 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:13:30.055 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:13:30.148 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:13:30.149 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:13:30.229 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:13:31.337 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:13:31.338 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:13:31.439 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:13:43.680 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:13:43.681 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:13:43.767 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:13:43.769 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:13:43.769 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:13:43.844 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:13:44.877 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:13:44.877 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:13:44.961 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:13:44.962 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:13:44.962 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:13:45.028 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:13:45.176 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:13:45.177 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:13:45.319 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:13:45.323 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:13:45.324 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:13:45.393 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:13:46.388 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:13:46.389 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:13:46.461 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:13:46.463 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:13:46.464 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:13:46.563 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:13:46.704 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:13:46.704 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:13:46.843 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:13:47.829 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:13:47.831 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:13:47.911 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:14:00.274 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:14:00.275 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:14:00.366 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:14:00.367 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:14:00.368 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:14:00.431 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:14:01.350 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:14:01.351 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:14:01.440 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:14:01.441 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:14:01.441 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:14:01.516 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:14:01.775 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:14:01.775 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:14:01.865 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:14:01.867 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:14:01.868 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:14:01.940 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:14:02.866 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:14:02.867 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:14:02.957 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:14:02.959 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:14:02.960 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:14:03.041 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:14:03.242 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:14:03.242 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:14:03.334 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:14:04.338 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:14:04.339 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:14:04.426 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:14:16.797 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:14:16.806 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:14:16.986 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:14:16.988 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:14:16.989 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:14:17.152 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:14:17.872 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:14:17.873 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:14:17.988 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:14:17.990 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:14:17.991 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:14:18.062 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:14:18.500 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:14:18.501 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:14:19.014 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:14:19.015 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:14:19.015 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:14:19.172 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:14:19.410 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:14:19.411 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:14:19.591 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:14:19.596 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:14:19.598 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:14:19.688 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:14:20.455 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:14:20.456 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:14:20.555 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:14:21.046 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:14:21.047 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:14:21.130 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:14:33.963 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:14:33.964 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:14:34.051 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:14:34.052 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:14:34.052 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:14:34.150 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:14:34.534 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:14:34.535 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:14:34.647 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:14:34.649 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:14:34.649 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:14:34.733 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:18:04.449 | INFO     | utils.db_manager:__init__:45 - 初始化数据库管理器，数据库路径: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:18:04.450 | DEBUG    | utils.db_manager:_connect:52 - 正在连接到数据库: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:18:04.451 | INFO     | utils.db_manager:_connect:56 - 数据库连接成功
2025-06-19 09:18:06.714 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:18:06.732 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:18:07.588 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:18:07.692 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:18:07.728 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:18:09.820 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:18:11.678 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:18:11.679 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:18:12.093 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:18:12.095 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:18:12.095 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:18:12.528 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:18:13.973 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:18:13.974 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:18:14.586 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:18:17.178 | INFO     | utils.db_manager:__init__:45 - 初始化数据库管理器，数据库路径: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:18:17.179 | DEBUG    | utils.db_manager:_connect:52 - 正在连接到数据库: F:\Development\Project\2025-06-17\oct_mcm_o2\data\temperature_cycling.db
2025-06-19 09:18:17.179 | INFO     | utils.db_manager:_connect:56 - 数据库连接成功
2025-06-19 09:18:19.355 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:18:19.356 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:18:20.497 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:18:20.498 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:18:20.498 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:18:20.646 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:18:22.089 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:18:22.091 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:18:22.213 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:18:22.214 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:18:22.214 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:18:22.314 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:18:23.500 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:18:23.501 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:18:23.598 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:18:27.998 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:18:27.999 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:18:28.118 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:18:28.122 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:18:28.123 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:18:28.217 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:18:29.406 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:18:29.407 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:18:29.499 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:18:29.506 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:18:29.506 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:18:29.574 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:18:31.184 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:18:31.185 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:18:31.445 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:18:36.907 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:18:36.908 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:18:36.986 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:18:36.999 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:18:37.000 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:18:37.094 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:18:38.284 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:18:38.285 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:18:38.555 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:18:38.558 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:18:38.559 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:18:38.770 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:18:39.965 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:18:39.965 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:18:40.118 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:18:44.810 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:18:44.811 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:18:45.055 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:18:45.058 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:18:45.058 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:18:45.322 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:18:46.494 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:18:46.495 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:18:46.813 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:18:46.816 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:18:46.816 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:18:47.097 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:18:48.268 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:18:48.269 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:18:48.397 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:18:53.387 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:18:53.388 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:18:53.513 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:18:53.523 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:18:53.524 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:18:53.658 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:18:54.858 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:18:54.859 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:18:55.041 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:18:55.043 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:18:55.044 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:18:55.124 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:18:56.283 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:18:56.284 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:18:56.377 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:19:01.704 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:19:01.705 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:19:01.814 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:19:01.820 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:19:01.821 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:19:01.900 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:19:03.075 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:19:03.075 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:19:03.227 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:19:03.229 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:19:03.230 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:19:03.301 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:19:04.457 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:19:04.458 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:19:04.886 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:19:09.747 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:19:09.748 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:19:10.121 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:19:10.123 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:19:10.123 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:19:10.520 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:19:11.681 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:19:11.682 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:19:11.997 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:19:11.999 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:19:11.999 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:19:12.521 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:19:13.662 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:19:13.663 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:19:14.022 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:19:18.175 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:19:18.176 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:19:18.282 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:19:18.291 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:19:18.292 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:19:18.393 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:19:19.565 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:19:19.565 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:19:19.649 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:19:19.651 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:19:19.651 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:19:19.775 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:19:20.923 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:19:20.924 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:19:20.998 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:19:27.254 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:19:27.255 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:19:27.344 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:19:27.348 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:19:27.349 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:19:27.460 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:19:28.626 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:19:28.627 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:19:28.726 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:19:28.728 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:19:28.729 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:19:28.792 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:19:29.914 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:19:29.915 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:19:30.112 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:19:34.292 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:19:34.293 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:19:35.180 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:19:35.182 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:19:35.183 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:19:35.754 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:19:38.171 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:19:38.172 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:19:39.088 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:19:39.090 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:19:39.091 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:19:39.872 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:19:41.904 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:19:41.905 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:19:42.638 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:19:43.405 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:19:43.406 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:19:43.997 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:19:44.001 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:19:44.002 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:19:44.340 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:19:45.534 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:19:45.535 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:19:45.923 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:19:45.924 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:19:45.925 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:19:46.354 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:19:47.520 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:19:47.521 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:19:47.890 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:19:57.092 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:19:57.092 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:19:57.243 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:19:57.245 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:19:57.246 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:19:57.403 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:19:58.582 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:19:58.583 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:19:58.794 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:19:58.795 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:19:58.796 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:19:59.003 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:20:00.194 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:20:00.195 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:20:00.637 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:20:01.207 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:20:01.208 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:20:01.385 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:20:01.387 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:20:01.388 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:20:01.595 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:20:02.824 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:20:02.825 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:20:03.004 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:20:03.006 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:20:03.006 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:20:03.222 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:20:04.463 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:20:04.464 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:20:04.929 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:20:14.131 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:20:14.131 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:20:14.407 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:20:14.408 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:20:14.409 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:20:14.827 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:20:16.058 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:20:16.058 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:20:16.209 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:20:16.213 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:20:16.214 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:20:16.329 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:20:17.473 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:20:17.474 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:20:17.642 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:20:18.591 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:20:18.591 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:20:19.159 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:20:19.160 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:20:19.161 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:20:19.657 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:20:20.863 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:20:20.863 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:20:21.110 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:20:21.111 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:20:21.112 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:20:21.276 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:20:22.460 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:20:22.461 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:20:22.676 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:20:30.965 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:20:30.966 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:20:31.063 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:20:31.064 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:20:31.065 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:20:31.171 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:20:32.519 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:20:32.519 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:20:32.638 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:20:32.640 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:20:32.640 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:20:32.822 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:20:33.948 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:20:33.949 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:20:34.090 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:20:36.057 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:20:36.058 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:20:36.197 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:20:36.199 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:20:36.200 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:20:36.323 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:20:37.552 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:20:37.553 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:20:37.715 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:20:37.717 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:20:37.717 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:20:37.789 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:20:38.958 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:20:38.959 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:20:39.133 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:20:47.406 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:20:47.407 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:20:47.486 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:20:47.488 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:20:47.489 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:20:47.567 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:20:48.765 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:20:48.766 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:20:48.860 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:20:48.861 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:20:48.862 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:20:48.943 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:20:50.122 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:20:50.123 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:20:50.327 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:20:52.486 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:20:52.487 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:20:52.770 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:20:52.776 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:20:52.777 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:20:53.095 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:20:54.543 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:20:54.544 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:20:54.635 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:20:54.637 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:20:54.637 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:20:54.744 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:20:55.904 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:20:55.905 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:20:56.037 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:21:03.951 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:21:03.952 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:21:04.057 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:21:04.062 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:21:04.064 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:21:04.132 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:21:05.324 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:21:05.325 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:21:05.740 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:21:05.741 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:21:05.741 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:21:06.233 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:21:07.399 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:21:07.400 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:21:07.841 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:21:09.313 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:21:09.314 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:21:09.625 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:21:09.626 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:21:09.626 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:21:10.009 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:21:11.364 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:21:11.364 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:21:11.717 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:21:11.718 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:21:11.718 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:21:12.509 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:21:13.666 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:21:13.667 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:21:14.042 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:21:21.344 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:21:21.344 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:21:21.540 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:21:21.543 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:21:21.544 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:21:21.704 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:21:22.911 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:21:22.912 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:21:23.096 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:21:23.097 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:21:23.098 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:21:23.245 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:21:24.410 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:21:24.410 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:21:24.504 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:21:27.368 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:21:27.369 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:21:27.448 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:21:27.449 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:21:27.450 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:21:27.564 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
2025-06-19 09:21:28.763 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200233
2025-06-19 09:21:28.764 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200233 插入数据，字段数: 32
2025-06-19 09:21:29.064 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200233 插入数据记录
2025-06-19 09:21:29.067 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200278
2025-06-19 09:21:29.067 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200278 插入数据，字段数: 32
2025-06-19 09:21:29.505 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200278 插入数据记录
2025-06-19 09:21:30.697 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200211
2025-06-19 09:21:30.698 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200211 插入数据，字段数: 32
2025-06-19 09:21:31.073 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200211 插入数据记录
2025-06-19 09:21:37.816 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200239
2025-06-19 09:21:37.817 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200239 插入数据，字段数: 32
2025-06-19 09:21:38.059 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200239 插入数据记录
2025-06-19 09:21:38.060 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200259
2025-06-19 09:21:38.061 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200259 插入数据，字段数: 32
2025-06-19 09:21:38.313 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200259 插入数据记录
2025-06-19 09:21:39.579 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200178
2025-06-19 09:21:39.580 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200178 插入数据，字段数: 32
2025-06-19 09:21:39.759 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200178 插入数据记录
2025-06-19 09:21:39.760 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200226
2025-06-19 09:21:39.761 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200226 插入数据，字段数: 32
2025-06-19 09:21:40.052 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200226 插入数据记录
2025-06-19 09:21:41.246 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200261
2025-06-19 09:21:41.247 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200261 插入数据，字段数: 32
2025-06-19 09:21:42.609 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200261 插入数据记录
2025-06-19 09:21:44.389 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200355
2025-06-19 09:21:44.389 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200355 插入数据，字段数: 32
2025-06-19 09:21:44.740 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200355 插入数据记录
2025-06-19 09:21:44.741 | DEBUG    | utils.db_manager:create_table_if_not_exists:119 - 创建表(如果不存在): AN252200206
2025-06-19 09:21:44.742 | DEBUG    | utils.db_manager:insert_data:134 - 向表 AN252200206 插入数据，字段数: 32
2025-06-19 09:21:44.937 | INFO     | utils.db_manager:insert_data:137 - 成功向表 AN252200206 插入数据记录
