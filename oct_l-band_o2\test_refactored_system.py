"""
重构系统测试脚本
用于验证新的组件化架构是否正常工作
"""

import sys
import os
import asyncio
from datetime import datetime
from typing import Dict, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.core.config_manager import ConfigManager
from service.core.connection_manager import ConnectionManager
from service.core.task_manager import TaskManager, TaskPriority
from service.core.data_collector import DataCollector
from service.core.validation_engine import ValidationEngine
from service.core.report_generator import ReportGenerator
from service.core.test_orchestrator import TestOrchestrator
from service.common.my_logger import logger


def test_config_manager():
    """测试配置管理器"""
    print("=" * 50)
    print("测试配置管理器")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager()
        
        # 测试基本配置获取
        app_ver = config_manager.get_config("app_ver", "unknown")
        print(f"应用版本: {app_ver}")
        
        # 测试连接配置
        conn_config = config_manager.get_connection_config()
        print(f"连接池大小: {conn_config.connection_pool_size}")
        print(f"连接超时: {conn_config.connection_timeout}")
        
        # 测试验证配置
        val_config = config_manager.get_validation_config()
        print(f"控制值列表长度: {len(val_config.control_value_list)}")
        print(f"输出键列表长度: {len(val_config.output_key_list)}")
        
        # 测试配置验证
        is_valid = config_manager.validate_config()
        print(f"配置验证结果: {is_valid}")
        
        print("✓ 配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False


def test_task_manager():
    """测试任务管理器"""
    print("=" * 50)
    print("测试任务管理器")
    print("=" * 50)
    
    try:
        task_manager = TaskManager(max_workers=2)
        task_manager.start()
        
        # 创建测试任务
        def test_task(x, y):
            import time
            time.sleep(0.1)  # 模拟工作
            return x + y
        
        # 提交任务
        task_id1 = task_manager.create_task(
            name="test_add_1",
            func=test_task,
            1, 2,
            priority=TaskPriority.HIGH
        )
        
        task_id2 = task_manager.create_task(
            name="test_add_2", 
            func=test_task,
            3, 4,
            priority=TaskPriority.NORMAL
        )
        
        # 等待任务完成
        result1 = task_manager.wait_for_task(task_id1, timeout=5.0)
        result2 = task_manager.wait_for_task(task_id2, timeout=5.0)
        
        print(f"任务1结果: {result1.result} (状态: {result1.status.value})")
        print(f"任务2结果: {result2.result} (状态: {result2.status.value})")
        
        # 获取统计信息
        stats = task_manager.get_statistics()
        print(f"任务统计: {stats}")
        
        task_manager.stop()
        print("✓ 任务管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 任务管理器测试失败: {e}")
        return False


def test_validation_engine():
    """测试验证引擎"""
    print("=" * 50)
    print("测试验证引擎")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager()
        validation_engine = ValidationEngine(config_manager)
        
        # 创建测试数据点
        from service.core.data_collector import DataPoint
        
        test_data_points = [
            DataPoint(
                timestamp=datetime.now(),
                source="TEST",
                module_id=1,
                parameter="Module Temperature",
                value=25.5,
                unit="degC",
                metadata={"sn": "TEST001"}
            ),
            DataPoint(
                timestamp=datetime.now(),
                source="TEST",
                module_id=1,
                parameter="Module Temperature",
                value=85.0,  # 超出范围
                unit="degC",
                metadata={"sn": "TEST001"}
            )
        ]
        
        # 验证数据
        validation_report = validation_engine.validate_module_data(1, test_data_points)
        
        print(f"验证报告:")
        print(f"  模块ID: {validation_report.module_id}")
        print(f"  总检查项: {validation_report.total_checks}")
        print(f"  通过项: {validation_report.passed_checks}")
        print(f"  失败项: {validation_report.failed_checks}")
        print(f"  是否通过: {validation_report.is_pass}")
        print(f"  错误数量: {len(validation_report.errors)}")
        
        # 获取统计信息
        stats = validation_engine.get_validation_statistics()
        print(f"验证统计: {stats}")
        
        print("✓ 验证引擎测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 验证引擎测试失败: {e}")
        return False


def test_report_generator():
    """测试报告生成器"""
    print("=" * 50)
    print("测试报告生成器")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager()
        report_generator = ReportGenerator(config_manager)
        
        # 创建测试数据
        from service.core.data_collector import DataPoint
        from service.core.validation_engine import ValidationReport, ValidationError
        
        test_data_points = [
            DataPoint(
                timestamp=datetime.now(),
                source="TEST",
                module_id=1,
                parameter="Module Temperature",
                value=25.5,
                unit="degC",
                metadata={"sn": "TEST001"}
            )
        ]
        
        test_validation_report = ValidationReport(
            timestamp=datetime.now(),
            module_id=1,
            total_checks=1,
            passed_checks=1,
            failed_checks=0,
            warning_checks=0,
            skipped_checks=0,
            errors=[]
        )
        
        # 生成报告
        module_sn_mapping = {1: "TEST001"}
        test_start_time = datetime.now()
        
        report_paths = report_generator.generate_module_reports(
            test_start_time=test_start_time,
            all_data_points=test_data_points,
            validation_reports={1: test_validation_report},
            module_sn_mapping=module_sn_mapping
        )
        
        print(f"生成的报告文件: {report_paths}")
        
        # 生成汇总报告
        summary_path = report_generator.generate_summary_report(
            test_start_time=test_start_time,
            validation_reports={1: test_validation_report},
            module_sn_mapping=module_sn_mapping
        )
        
        print(f"汇总报告文件: {summary_path}")
        
        # 获取统计信息
        stats = report_generator.get_report_statistics()
        print(f"报告统计: {stats}")
        
        print("✓ 报告生成器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 报告生成器测试失败: {e}")
        return False


async def test_orchestrator():
    """测试编排器（模拟测试）"""
    print("=" * 50)
    print("测试编排器（模拟）")
    print("=" * 50)
    
    try:
        orchestrator = TestOrchestrator()
        
        # 获取初始状态
        status = orchestrator.get_test_status()
        print(f"初始状态: {status}")
        
        # 获取统计信息
        stats = orchestrator.get_statistics()
        print(f"统计信息: {stats}")
        
        print("✓ 编排器基础测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 编排器测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始重构系统测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行各个组件测试
    test_results.append(("配置管理器", test_config_manager()))
    test_results.append(("任务管理器", test_task_manager()))
    test_results.append(("验证引擎", test_validation_engine()))
    test_results.append(("报告生成器", test_report_generator()))
    
    # 运行异步测试
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(test_orchestrator())
        test_results.append(("编排器", result))
    except Exception as e:
        print(f"编排器测试异常: {e}")
        test_results.append(("编排器", False))
    finally:
        loop.close()
    
    # 输出测试结果
    print("=" * 80)
    print("测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构系统基础功能正常")
        return True
    else:
        print("⚠️  部分测试失败，需要检查相关组件")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
