{"app_name": "OCT Thermal Test V5", "app_version": "5.0.0", "app_description": "基于PySide6的现代化温循测试上位机系统", "oct_devices": [{"device_id": "oct_1", "name": "OCT设备1", "connection_type": "ssh", "host": "*************", "port": 22, "username": "admin", "password": "Admin_123", "timeout": 30, "retry_count": 3, "retry_delay": 1.0, "slots": [1, 2, 3, 4], "device_type": "cfp2_dco", "data_fields": [{"name": "temperature", "display_name": "模块温度", "data_type": "float", "unit": "°C", "format_string": "{:.1f}", "parser_function": null, "enabled": true}, {"name": "optical_power", "display_name": "光功率", "data_type": "float", "unit": "dBm", "format_string": "{:.2f}", "parser_function": null, "enabled": true}, {"name": "bias_current", "display_name": "偏置电流", "data_type": "float", "unit": "mA", "format_string": "{:.1f}", "parser_function": null, "enabled": true}], "baudrate": 9600, "bytesize": 8, "parity": "N", "stopbits": 1, "enabled": true}], "npb_devices": [{"device_id": "npb_1", "name": "NPB设备1", "host": "*************", "port": 80, "username": "admin", "password": "Admin_123", "timeout": 30, "retry_count": 3, "ports": ["1", "2"], "device_type": "standard_npb", "api_version": "v1", "enabled": true}, {"device_id": "npb_2", "name": "NPB设备2", "host": "*************", "port": 80, "username": "admin", "password": "Admin_123", "timeout": 30, "retry_count": 3, "ports": ["1", "2"], "device_type": "standard_npb", "api_version": "v1", "enabled": true}], "mes": {"enabled": false, "base_url": "", "username": "", "password": "", "timeout": 30}, "test": {"loop_interval": 10, "stability_timeout": 300, "stability_check_interval": 10, "post_stability_wait_time": 120, "max_workers": 4, "task_timeout": 60, "max_retries": 3, "retry_delay": 1.0, "validation_rules": [{"name": "Module Temperature", "parameter": "Module Temperature", "min_value": 0.0, "max_value": 70.0, "value_type": "float", "rule_type": "range", "enabled": true, "unit": "°C", "description": "模块温度检查", "sn_specific": false, "target_sn": null, "dynamic_threshold": false}, {"name": "<PERSON><PERSON>", "parameter": "<PERSON><PERSON>", "min_value": 0.0, "max_value": 100.0, "value_type": "float", "rule_type": "range", "enabled": true, "unit": "mA", "description": "激光器偏置电流检查", "sn_specific": false, "target_sn": null, "dynamic_threshold": false}, {"name": "Optical Power", "parameter": "Optical Power", "min_value": -10.0, "max_value": 5.0, "value_type": "float", "rule_type": "range", "enabled": true, "unit": "dBm", "description": "光功率检查", "sn_specific": false, "target_sn": null, "dynamic_threshold": false}, {"name": "Network FEC Uncorr Blk Cnt", "parameter": "Network FEC Uncorr Blk Cnt", "min_value": -999.0, "max_value": -999.0, "value_type": "int", "rule_type": "increase", "enabled": true, "unit": "", "description": "网络FEC不可纠正块计数检查", "sn_specific": true, "target_sn": null, "dynamic_threshold": false}], "output_path": "./output", "auto_generate_reports": true, "report_format": "excel"}, "ui": {"theme": "light", "language": "zh_CN", "window_size": [1200, 800], "window_position": null, "auto_save_layout": true, "refresh_interval": 1000}, "logging": {"level": "INFO", "file_enabled": true, "file_path": "./logs", "file_rotation": "1 day", "file_retention": "30 days", "console_enabled": true, "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"}, "debug_mode": false, "performance_monitoring": false, "auto_backup": true, "backup_interval": 3600}