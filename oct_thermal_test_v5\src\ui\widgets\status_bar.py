"""
状态栏组件
显示应用程序状态信息
"""

from PySide6.QtWidgets import (
    QStatusBar, QLabel, QProgressBar, QPushButton, 
    QHBoxLayout, QWidget, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QPixmap, QIcon

from src.utils.logger import LoggerMixin


class StatusBarWidget(QStatusBar, LoggerMixin):
    """状态栏组件"""
    
    # 信号定义
    settings_requested = Signal()
    about_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._setup_ui()
        self._setup_timer()
    
    def _setup_ui(self):
        """设置用户界面"""
        # 1. 主状态消息
        self.status_label = QLabel("就绪")
        self.addWidget(self.status_label)
        
        # 2. 分隔符
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFrameShadow(QFrame.Sunken)
        self.addWidget(separator1)
        
        # 3. 设备连接状态
        self.device_status_widget = self._create_device_status_widget()
        self.addWidget(self.device_status_widget)
        
        # 4. 分隔符
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        self.addWidget(separator2)
        
        # 5. 测试进度
        self.progress_widget = self._create_progress_widget()
        self.addWidget(self.progress_widget)
        
        # 6. 分隔符
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.VLine)
        separator3.setFrameShadow(QFrame.Sunken)
        self.addWidget(separator3)
        
        # 7. 系统信息
        self.system_info_widget = self._create_system_info_widget()
        self.addPermanentWidget(self.system_info_widget)
        
        # 8. 设置按钮
        self.settings_btn = QPushButton("⚙")
        self.settings_btn.setToolTip("设置")
        self.settings_btn.setMaximumWidth(30)
        self.settings_btn.clicked.connect(self.settings_requested.emit)
        self.addPermanentWidget(self.settings_btn)
        
        # 9. 关于按钮
        self.about_btn = QPushButton("?")
        self.about_btn.setToolTip("关于")
        self.about_btn.setMaximumWidth(30)
        self.about_btn.clicked.connect(self.about_requested.emit)
        self.addPermanentWidget(self.about_btn)
    
    def _create_device_status_widget(self):
        """创建设备状态组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 0, 5, 0)
        
        # OCT状态
        self.oct_status_label = QLabel("OCT: 未连接")
        self.oct_status_label.setStyleSheet("color: red;")
        layout.addWidget(self.oct_status_label)
        
        # NPB状态
        self.npb_status_label = QLabel("NPB: 未连接")
        self.npb_status_label.setStyleSheet("color: red;")
        layout.addWidget(self.npb_status_label)
        
        return widget
    
    def _create_progress_widget(self):
        """创建进度组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 0, 5, 0)
        
        self.progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(100)
        self.progress_bar.setVisible(False)
        
        layout.addWidget(self.progress_label)
        layout.addWidget(self.progress_bar)
        
        return widget
    
    def _create_system_info_widget(self):
        """创建系统信息组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 0, 5, 0)
        
        # CPU使用率
        self.cpu_label = QLabel("CPU: 0%")
        layout.addWidget(self.cpu_label)
        
        # 内存使用率
        self.memory_label = QLabel("内存: 0MB")
        layout.addWidget(self.memory_label)
        
        # 当前时间
        self.time_label = QLabel()
        layout.addWidget(self.time_label)
        
        return widget
    
    def _setup_timer(self):
        """设置定时器"""
        # 系统信息更新定时器
        self.system_timer = QTimer()
        self.system_timer.timeout.connect(self._update_system_info)
        self.system_timer.start(1000)  # 每秒更新
    
    def _update_system_info(self):
        """更新系统信息"""
        import psutil
        from datetime import datetime
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
            
            # 内存使用
            memory = psutil.virtual_memory()
            memory_mb = memory.used / 1024 / 1024
            self.memory_label.setText(f"内存: {memory_mb:.0f}MB")
            
            # 当前时间
            current_time = datetime.now().strftime("%H:%M:%S")
            self.time_label.setText(current_time)
            
        except ImportError:
            # 如果没有psutil，使用模拟数据
            import random
            from datetime import datetime
            
            cpu_percent = random.randint(5, 25)
            memory_mb = random.randint(100, 300)
            current_time = datetime.now().strftime("%H:%M:%S")
            
            self.cpu_label.setText(f"CPU: {cpu_percent}%")
            self.memory_label.setText(f"内存: {memory_mb}MB")
            self.time_label.setText(current_time)
    
    def set_status_message(self, message: str, timeout: int = 0):
        """设置状态消息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))
        self.log_debug(f"状态消息: {message}")
    
    def set_oct_status(self, connected: bool, message: str = ""):
        """设置OCT连接状态"""
        if connected:
            self.oct_status_label.setText(f"OCT: 已连接 {message}")
            self.oct_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.oct_status_label.setText(f"OCT: 未连接 {message}")
            self.oct_status_label.setStyleSheet("color: red;")
    
    def set_npb_status(self, connected: bool, count: int = 0, message: str = ""):
        """设置NPB连接状态"""
        if connected:
            self.npb_status_label.setText(f"NPB: 已连接({count}) {message}")
            self.npb_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.npb_status_label.setText(f"NPB: 未连接 {message}")
            self.npb_status_label.setStyleSheet("color: red;")
    
    def show_progress(self, show: bool = True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_label.setText("进度:")
        else:
            self.progress_label.setText("")
    
    def set_progress(self, value: int, maximum: int = 100):
        """设置进度值"""
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(value)
        
        if maximum > 0:
            percentage = (value / maximum) * 100
            self.progress_label.setText(f"进度: {percentage:.1f}%")
    
    def set_test_status(self, status: str):
        """设置测试状态"""
        status_messages = {
            "idle": "空闲",
            "connecting": "连接中",
            "stability_check": "稳定性检查",
            "testing": "测试中",
            "generating_reports": "生成报告",
            "completed": "测试完成",
            "failed": "测试失败",
            "cancelled": "测试取消"
        }
        
        message = status_messages.get(status, status)
        self.set_status_message(message)
        
        # 根据状态设置颜色
        if status in ["testing", "connecting", "stability_check"]:
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        elif status == "completed":
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        elif status in ["failed", "cancelled"]:
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("color: black;")
    
    def show_temporary_message(self, message: str, duration: int = 3000):
        """显示临时消息"""
        original_text = self.status_label.text()
        original_style = self.status_label.styleSheet()
        
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        
        # 恢复原始消息
        QTimer.singleShot(duration, lambda: (
            self.status_label.setText(original_text),
            self.status_label.setStyleSheet(original_style)
        ))
    
    def flash_message(self, message: str, color: str = "red", count: int = 3):
        """闪烁消息"""
        original_text = self.status_label.text()
        original_style = self.status_label.styleSheet()
        
        flash_style = f"color: {color}; font-weight: bold;"
        
        def toggle_flash(remaining_count):
            if remaining_count <= 0:
                self.status_label.setText(original_text)
                self.status_label.setStyleSheet(original_style)
                return
            
            # 切换显示
            if remaining_count % 2 == 1:
                self.status_label.setText(message)
                self.status_label.setStyleSheet(flash_style)
            else:
                self.status_label.setText("")
                self.status_label.setStyleSheet("")
            
            QTimer.singleShot(500, lambda: toggle_flash(remaining_count - 1))
        
        toggle_flash(count * 2)
    
    def update_connection_count(self, oct_connected: bool, npb_count: int):
        """更新连接计数"""
        self.set_oct_status(oct_connected)
        self.set_npb_status(npb_count > 0, npb_count)
        
        total_expected = 1 + 2  # 1个OCT + 2个NPB（示例）
        total_connected = (1 if oct_connected else 0) + npb_count
        
        if total_connected == total_expected:
            self.set_status_message("所有设备已连接")
        elif total_connected > 0:
            self.set_status_message(f"部分设备已连接 ({total_connected}/{total_expected})")
        else:
            self.set_status_message("无设备连接")
    
    def clear_all_status(self):
        """清空所有状态"""
        self.set_status_message("就绪")
        self.set_oct_status(False)
        self.set_npb_status(False)
        self.show_progress(False)
