<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E302" />
          <option value="E711" />
          <option value="W605" />
          <option value="E722" />
          <option value="E712" />
          <option value="E741" />
          <option value="E127" />
          <option value="W292" />
          <option value="E402" />
          <option value="E401" />
          <option value="E303" />
          <option value="E701" />
          <option value="E128" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
          <option value="N802" />
          <option value="N806" />
          <option value="N803" />
          <option value="N814" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingBuiltinsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredNames">
        <list>
          <option value="exec" />
          <option value="type" />
          <option value="sum" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="PySide6.QtCore.Qt.BlockingQueuedConnection" />
          <option value="log.default_logging_conf._log_dir" />
          <option value="object.setStyleSheet" />
          <option value="SVN_STATUS_MAP" />
          <option value="requests" />
          <option value="progress" />
          <option value="requests.sessions.Session.timeout" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>