"""
简化版主窗口 - 只有开始/结束挂机按钮 + 数据表格 + 日志 + MES登录
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QTextEdit, QLineEdit, QFrame,
    QMessageBox
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction, QFont

from config.settings import Settings
from src.utils.logger import LoggerMixin
from src.ui.widgets.simple_test_table import SimpleTestTableWidget


class MainWindow(QMainWindow, LoggerMixin):
    """简化版主窗口"""
    
    def __init__(self, settings: Settings, test_engine, device_manager):
        super().__init__()
        
        self.settings = settings
        self.test_engine = test_engine
        self.device_manager = device_manager
        
        # UI组件
        self.test_table = None
        self.log_text = None
        self.start_btn = None
        self.stop_btn = None
        self.runtime_label = None
        self.mes_login_edit = None
        
        # 状态
        self._test_running = False
        self._start_time = None
        self._runtime_timer = None
        
        self._setup_ui()
        self._setup_menu_bar()
        self._setup_connections()
        self._load_layout()
        
        # 初始化数据
        self._initialize_test_modules()
    
    def _setup_ui(self):
        """Setup user interface"""
        self.setWindowTitle("OCT Thermal Test V5 - 温循挂机测试")
        self.setMinimumSize(1000, 700)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 1. 顶部控制栏
        top_layout = QHBoxLayout()
        
        # 左侧：操作按钮
        self.start_btn = QPushButton("开始挂机")
        self.start_btn.setMinimumSize(120, 40)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover { background-color: #45a049; }
            QPushButton:disabled { background-color: #cccccc; }
        """)
        
        self.stop_btn = QPushButton("结束挂机")
        self.stop_btn.setMinimumSize(120, 40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover { background-color: #da190b; }
            QPushButton:disabled { background-color: #cccccc; }
        """)
        
        # 右侧：已挂机时间
        self.runtime_label = QLabel("已挂机时间: 00:00:00")
        self.runtime_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                padding: 10px;
                background-color: #f0f0f0;
                border-radius: 5px;
            }
        """)
        
        top_layout.addWidget(self.start_btn)
        top_layout.addWidget(self.stop_btn)
        top_layout.addStretch()
        top_layout.addWidget(self.runtime_label)
        
        main_layout.addLayout(top_layout)
        
        # 2. 中间：数据表格
        self.test_table = SimpleTestTableWidget()
        main_layout.addWidget(self.test_table)
        
        # 3. 底部：日志和MES登录
        bottom_layout = QHBoxLayout()
        
        # 左侧：日志显示
        log_frame = QFrame()
        log_frame.setFrameStyle(QFrame.StyledPanel)
        log_layout = QVBoxLayout(log_frame)
        
        log_label = QLabel("系统日志")
        log_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        log_layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        # 右侧：MES登录
        mes_frame = QFrame()
        mes_frame.setFrameStyle(QFrame.StyledPanel)
        mes_frame.setMaximumWidth(250)
        mes_layout = QVBoxLayout(mes_frame)
        
        mes_label = QLabel("MES登录")
        mes_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        mes_layout.addWidget(mes_label)
        
        self.mes_login_edit = QLineEdit()
        self.mes_login_edit.setPlaceholderText("输入MES用户名...")
        mes_layout.addWidget(self.mes_login_edit)
        
        mes_login_btn = QPushButton("登录MES")
        mes_login_btn.clicked.connect(self._show_mes_login)
        mes_layout.addWidget(mes_login_btn)
        
        mes_layout.addStretch()
        
        bottom_layout.addWidget(log_frame, 3)
        bottom_layout.addWidget(mes_frame, 1)
        
        main_layout.addLayout(bottom_layout)
        
        # 添加初始日志
        self._add_log("系统启动完成")
        self._add_log("等待开始挂机测试...")
    
    def _setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 配置菜单
        config_menu = menubar.addMenu("配置(&C)")
        
        device_config_action = QAction("设备配置(&D)", self)
        device_config_action.triggered.connect(self._show_device_config)
        config_menu.addAction(device_config_action)
        
        test_config_action = QAction("测试配置(&T)", self)
        test_config_action.triggered.connect(self._show_test_config)
        config_menu.addAction(test_config_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.start_btn.clicked.connect(self._start_test)
        self.stop_btn.clicked.connect(self._stop_test)
        
        # 表格信号
        self.test_table.module_failed.connect(self._on_module_failed)
        
        # 运行时间定时器
        self._runtime_timer = QTimer()
        self._runtime_timer.timeout.connect(self._update_runtime)
    
    def _initialize_test_modules(self):
        """初始化测试模块"""
        # 根据配置添加测试模块
        for oct_device in self.settings.oct_devices:
            if not oct_device.enabled:
                continue
                
            for slot in oct_device.slots:
                # 查找对应的NPB设备
                npb_device = "未配置"
                for npb in self.settings.npb_devices:
                    if npb.enabled:
                        npb_device = f"{npb.name}({npb.host})"
                        break
                
                self.test_table.add_test_module(
                    slot, 
                    f"{oct_device.name}({oct_device.host})",
                    npb_device
                )
        
        self._add_log(f"初始化了 {self.test_table.rowCount()} 个测试模块")
    
    def _start_test(self):
        """开始挂机测试"""
        if self._test_running:
            return
        
        self.log_info("开始挂机测试")
        self._test_running = True
        
        # 更新按钮状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 记录开始时间
        from datetime import datetime
        self._start_time = datetime.now()
        
        # 启动运行时间定时器
        self._runtime_timer.start(1000)  # 每秒更新
        
        # 开始数据模拟
        self.test_table.start_simulation()
        
        self._add_log("挂机测试已开始")
        self._add_log("系统将持续监控模块状态...")
    
    def _stop_test(self):
        """结束挂机测试"""
        if not self._test_running:
            return
        
        self.log_info("结束挂机测试")
        self._test_running = False
        
        # 更新按钮状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 停止定时器
        self._runtime_timer.stop()
        
        # 停止数据模拟
        self.test_table.stop_simulation()
        
        # 显示测试结果
        self._show_test_results()
        
        self._add_log("挂机测试已结束")
    
    def _update_runtime(self):
        """更新运行时间显示"""
        if self._start_time:
            from datetime import datetime
            elapsed = datetime.now() - self._start_time
            
            hours = int(elapsed.total_seconds() // 3600)
            minutes = int((elapsed.total_seconds() % 3600) // 60)
            seconds = int(elapsed.total_seconds() % 60)
            
            self.runtime_label.setText(f"已挂机时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
    
    def _show_test_results(self):
        """显示测试结果"""
        summary = self.test_table.get_test_summary()
        failed_modules = self.test_table.get_failed_modules()
        
        # 构建结果消息
        result_text = f"""挂机测试结果:

总模块数: {summary['total']}
成功模块: {summary['success']}
失败模块: {summary['failed']}
成功率: {summary['success_rate']:.1f}%"""
        
        if failed_modules:
            result_text += f"\n\n失败模块列表:\n"
            for slot in failed_modules:
                result_text += f"- 插槽 {slot}\n"
        
        # 根据结果选择图标
        if summary['failed'] == 0:
            QMessageBox.information(self, "测试完成", result_text)
        else:
            QMessageBox.warning(self, "测试完成", result_text)
    
    def _on_module_failed(self, slot: int, reason: str):
        """处理模块失败"""
        self._add_log(f"模块失败 - 插槽 {slot}: {reason}", "ERROR")
    
    def _add_log(self, message: str, level: str = "INFO"):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        color = "black"
        if level == "ERROR":
            color = "red"
        elif level == "WARNING":
            color = "orange"
        elif level == "SUCCESS":
            color = "green"
        
        log_entry = f'<span style="color: {color}">[{timestamp}] {level}: {message}</span>'
        self.log_text.append(log_entry)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def _show_device_config(self):
        """显示设备配置对话框"""
        QMessageBox.information(self, "设备配置", "设备配置功能正在开发中...")
    
    def _show_test_config(self):
        """显示测试配置对话框"""
        QMessageBox.information(self, "测试配置", "测试配置功能正在开发中...")
    
    def _show_mes_login(self):
        """显示MES登录对话框"""
        QMessageBox.information(self, "MES登录", "MES登录功能正在开发中...")
    
    def _show_about(self):
        """显示关于对话框"""
        about_text = f"""OCT Thermal Test V5

版本: {self.settings.app_version}
描述: 简化版温循挂机测试系统

主要特性:
• 简洁的操作界面
• 支持多OCT和多NPB设备
• 实时状态监控
• 失败模块红色标识
• MES系统集成"""
        
        QMessageBox.about(self, "关于", about_text)
    
    def _load_layout(self):
        """加载窗口布局"""
        if self.settings.ui.window_size:
            self.resize(*self.settings.ui.window_size)
        
        if self.settings.ui.window_position:
            self.move(*self.settings.ui.window_position)
    
    def save_layout(self):
        """保存窗口布局"""
        self.log_info("保存窗口布局")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self._test_running:
            reply = QMessageBox.question(
                self, "确认退出", 
                "挂机测试正在进行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
            
            # 停止测试
            self._stop_test()
        
        self.save_layout()
        self.log_info("应用程序退出")
        event.accept()
