"""
@Time        : 2025/5/12 14:18
<AUTHOR> 53211
@File        : serial_utils.py
@Project     : TempCycle-Controller
@Description : 
"""
import copy
import datetime
import json
import re
import pandas as pd

import serial
import time
import threading
import os
import sys
from queue import Queue
from typing import Optional

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入DBManager以统一数据库操作
from utils.db_manager import DBManager


class SerialCLI:
    def __init__(self):
        self.response_queue = Queue()
        self.serial: Optional[serial.Serial] = None
        self.is_running = False
        self.receive_thread = None
        self.command_queue = Queue()
        with open("config/config.json", 'r', encoding='utf-8') as f:
            self.header_config = json.load(f)
        self.header_list = list(self.header_config.keys())
        self.result_df = pd.DataFrame(
            columns=self.header_list)
        self.start_time = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        # 使用DBManager代替直接的数据库访问
        self.db_manager = DBManager()

    def init_serial(self, port: str, baudrate: int = 115200):
        """初始化串口"""
        try:
            self.serial = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            return True
        except Exception as e:
            print(f"串口初始化失败: {e}")
            return False

    def start(self):
        """启动串口会话"""
        if not self.serial:
            print("请先初始化串口")
            return

        if not self.serial.is_open:
            try:
                self.serial.open()
            except Exception as e:
                print(f"打开串口失败: {e}")
                return

        self.is_running = True

        # 启动接收线程
        self.receive_thread = threading.Thread(target=self._receive_loop)
        self.receive_thread.daemon = True
        self.receive_thread.start()

        # 启动接收线程
        self.run_task_periodically(10, self.check_slot_has_module)

    @staticmethod
    def run_task_periodically(interval, task):
        """
        使用一个独立的线程以固定的间隔执行给定的任务。

        :param interval: 执行任务的时间间隔（秒）
        :param task: 要执行的任务函数
        """

        def wrapper():
            while True:
                task(True)
                time.sleep(interval)

        t = threading.Thread(target=wrapper, daemon=True)
        t.start()
        t.join()

    def stop(self):
        """停止串口会话"""
        self.is_running = False
        if self.receive_thread:
            self.receive_thread.join()

        if self.serial and self.serial.is_open:
            self.serial.close()

    def _send_command(self, command: str):
        """发送命令"""
        if not self.serial or not self.serial.is_open:
            return
        # 清空之前的响应
        while not self.response_queue.empty():
            self.response_queue.get()
        try:
            # 确保命令以\r\n结束
            if not command.endswith('\r\n'):
                command += '\r\n'

            self.serial.write(command.encode())
            self.serial.flush()
        except Exception as e:
            print(f"发送失败: {e}")

    def _receive_loop(self):
        """接收数据循环"""
        buffer = ""
        while self.is_running:
            if not self.serial or not self.serial.is_open:
                break
            try:
                if self.serial.in_waiting:
                    data = self.serial.read(self.serial.in_waiting)
                    try:
                        text = data.decode('utf-8')
                        buffer += text

                        # 处理完整的行
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.rstrip('\r')
                            if "<Accelink>" in line:
                                self._send_command("sys")
                            self.response_queue.put(line + '\n')

                        # 处理不完整的行（可能是提示符）
                        if buffer and (']' in buffer or '>' in buffer or '#' in buffer):
                            self.response_queue.put(buffer)
                            buffer = ""

                        # 检查是否有 --More-- 提示
                        if '--More--' in buffer or '--more--' in buffer.lower():
                            self.serial.write(b' ')
                            buffer = buffer.replace('--More--', '').replace('--more--', '')

                    except UnicodeDecodeError:
                        print(f"收到二进制数据: {data.hex()}")
                        buffer = ""

            except Exception as e:
                print(f"接收数据错误: {e}")
                break

            time.sleep(0.01)

    def check_slot_has_module(self, is_save_file=False):
        self._send_command("sys")
        self._send_command("exit")
        self._send_command("display ip-address")
        # 清空响应队列
        while not self.response_queue.empty():
            self.response_queue.get()
        response_lines = ""
        start_time = time.time()
        while time.time() - start_time < 1:
            try:
                response = self.response_queue.get(timeout=0.1)
                if response:
                    response_lines += response
            except Exception as e:
                # 队列超时，继续等待
                continue
        ip_pattern = "\s*NMS\(eth2.2\) ip addr\s*:\s*(.*)\n"
        ip_result = re.findall(ip_pattern, response_lines)
        if len(ip_result) > 0:
            ip = ip_result[0]
        else:
            ip = ""
        slots = range(1, 5)
        slot_result_list = []
        for slot in slots:
            # 清空响应队列
            while not self.response_queue.empty():
                self.response_queue.get()
            # 步骤1进入指定槽位
            self._send_command(f"slot 1/{slot}")
            # 清空响应队列
            while not self.response_queue.empty():
                self.response_queue.get()
            # 查看槽位模块信息
            self._send_command("display cfp2-dco")
            response_lines = ""
            # 等待响应
            start_time = time.time()
            while time.time() - start_time < 1:
                try:
                    response = self.response_queue.get(timeout=0.1)
                    if response:
                        response_lines += response
                except Exception as e:
                    # print(f"错误 {e}")
                    # 队列超时，继续等待
                    continue
            # 使用正则表达式匹配 "There are X CFP2_DCO module online:"
            pattern = r"There are (\d+) CFP2_DCO module online:"
            match = re.search(pattern, response_lines)
            if match:
                module_count = int(match.group(1))
                print(f"检测到 {module_count} 个模块")
                if module_count == 1:
                    # 使用正则表达式匹配 "There are X CFP2_DCO module online:"
                    pattern = r"CFP2_DCO Module No.(\d) information:"
                    match = re.search(pattern, response_lines)
                    if match:
                        module_location = int(match.group(1)) - 1
                        slot_result_list.append((slot - 1, module_location))
                        if is_save_file:
                            self.parse_data(response_lines, ip, f"1/{slot}-{module_location + 1}", is_save_file)
                elif module_count == 2:
                    slot_result_list.append((slot - 1, 0))
                    slot_result_list.append((slot - 1, 1))
                    if is_save_file:
                        self.parse_data(response_lines, ip, f"1/{slot}", is_save_file)

        return slot_result_list

    @staticmethod
    def split_strings_into_four_parts(string_array):
        """
        将一个字符串数组按空格拆分为四个部分。

        :param string_array: 包含待分割字符串的列表
        :return: 四个列表，分别存储了所有输入字符串的第一、第二、第三和第四部分
        """
        # 初始化四个列表用于存放分割后的数据

        # 初始化一个包含四个列表的列表
        all_parts = [[], [], [], []]

        for index, item in enumerate(string_array):
            # 使用' '作为分隔符，最多分割成4部分
            split_parts = item.split(' ', 3)

            if len(split_parts) == 4:
                for i, part in enumerate(split_parts):
                    all_parts[i].append(part)
            else:
                print(f"警告: 第 {index} 个字符串 '{item}' 无法被准确地分割成4个部分")

        # 直接返回包含四个子列表的大列表
        return all_parts

    def parse_data(self, content, ip, slot, is_save_file):
        """
        解析获取到的模块挂机数据
        :param content: 数据全部内容
        :param ip: 模块所属夹具的ip
        :param slot: 模块所属的夹具曹伟
        :param is_save_file: 是否保存文件
        """
        # 初始化模块数据
        module_info_list = []
        # 根据表头数组分别解析模块信息数据
        for header in self.header_list:
            if header in [" ", "  ", "   ", "    ", "     ", "      ", "       ", "        ", "         ", "          ",
                          "           ", "            "]:
                continue
            # 定义统一的解析字符串
            pattern = f"\s*{header}\s*:\s*(.*)\s*"
            # 特殊的解析字符串
            if header == "Module Temperature":
                pattern = "\s*Module Temperature\s*:\s*(.*) C\s*"
            elif header == "Module Power Supply":
                pattern = "\s*Module Power Supply\s*:\s*(.*) V\s*"
            elif header == "SOA Bias":
                pattern = "\s*SOA Bias\s*:\s*(.*) mA\s*"
            elif header == "Tx Laser Bias":
                pattern = "\s*Tx Laser Bias\s*:\s*(.*) mA\s*"
            elif header == "Network TX Laser Temp":
                pattern = "\s*Network TX Laser Temp\s*:\s*(.*) C\s*"
            elif header == "RX Current Freq":
                pattern = "\s*RX Current Freq\s*:\s*(.*) GHz\s*"
            elif header == "Current Output Power":
                pattern = "\s*Current Output Power\s*:\s*(.*) dBm\s*"
            elif header == "Current Input Power":
                pattern = "\s*Current Input Power\s*:\s*(.*) dBm\s*"
            result = re.findall(pattern, content)
            if header == "Vendor SN":
                result = [re.sub(r"\s+", "", s) for s in result]
            if header == "Client Tx FEC Uncorr Blk SMR":
                module_info_list.extend(self.split_strings_into_four_parts(result))
            elif header == "Client Tx FEC Corr Bit SMR":
                module_info_list.extend(self.split_strings_into_four_parts(result))
            elif header == "Client Tx Post FEC Ber PM-Int":
                module_info_list.extend(self.split_strings_into_four_parts(result))
            elif header == "Client Tx FEC Ber over PM-Int":
                module_info_list.extend(self.split_strings_into_four_parts(result))
            else:
                module_info_list.append(result)
        result_df = pd.DataFrame(zip(*module_info_list), columns=self.header_list)
        result_df['时间'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        result_df['ip'] = ip
        if result_df.shape[0] == 2:
            result_df.loc[0, '槽位'] = f"{slot}-1"
            result_df.loc[1, '槽位'] = f"{slot}-2"
        else:
            result_df['槽位'] = slot
        result_df['Network FEC Uncorr Blk Cnt'] = result_df['Network FEC Uncorr Blk Cnt'].apply(
            lambda x: int(x, 16) if isinstance(x, str) and x.startswith('0x') else x)
        result_df = result_df.astype(self.header_config)

        # 使用DBManager存储数据，而不是直接使用SQLiteDatabase
        for i in range(result_df.shape[0]):
            # 使用模块SN作为表名
            table_name = result_df['Vendor SN'][i]
            if not table_name or table_name.strip() == '':
                continue  # 跳过没有SN的数据

            data = {
                'record_time': result_df['时间'][i],
                'ip': result_df['ip'][i],
                'position_in_slot': result_df['槽位'][i],
                'vendor_sn': result_df['Vendor SN'][i],
                'module_temperature': result_df['Module Temperature'][i],
                'module_power_supply': result_df['Module Power Supply'][i],
                'soa_bias': result_df['SOA Bias'][i],
                'tx_laser_bias': result_df['Tx Laser Bias'][i],
                'network_tx_laser_temp': result_df['Network TX Laser Temp'][i],
                'rx_current_freq': result_df['RX Current Freq'][i],
                'current_output_power': result_df['Current Output Power'][i],
                'current_input_power': result_df['Current Input Power'][i],
                'network_current_ber': result_df['Network Current BER'][i],
                'network_fec_uncorr_blk_cnt': result_df['Network FEC Uncorr Blk Cnt'][i],
                'network_fec_uncorr_blk_reset': result_df['Network FEC Uncorr Blk Reset'][i],
                'network_fec_uncorr_blk_rate': result_df['Network FEC Uncorr Blk Rate'][i],
                'client_tx_fec_uncorr_blk_smr': result_df['Client Tx FEC Uncorr Blk SMR'][i],
                'client_tx_fec_uncorr_blk_smr_2': result_df[' '][i],
                'client_tx_fec_uncorr_blk_smr_3': result_df['  '][i],
                'client_tx_fec_uncorr_blk_smr_4': result_df['   '][i],
                'client_tx_fec_corr_bit_smr': result_df['Client Tx FEC Corr Bit SMR'][i],
                'client_tx_fec_corr_bit_smr_2': result_df['    '][i],
                'client_tx_fec_corr_bit_smr_3': result_df['     '][i],
                'client_tx_fec_corr_bit_smr_4': result_df['      '][i],
                'client_tx_post_fec_ber_pm_int': result_df['Client Tx Post FEC Ber PM-Int'][i],
                'client_tx_post_fec_ber_pm_int_2': result_df['       '][i],
                'client_tx_post_fec_ber_pm_int_3': result_df['        '][i],
                'client_tx_post_fec_ber_pm_int_4': result_df['         '][i],
                'client_tx_fec_ber_over_pm_int': result_df['Client Tx FEC Ber over PM-Int'][i],
                'client_tx_fec_ber_over_pm_int_2': result_df['          '][i],
                'client_tx_fec_ber_over_pm_int_3': result_df['           '][i],
                'client_tx_fec_ber_over_pm_int_4': result_df['            '][i],
            }
            print(data)
            # 使用DBManager插入数据
            self.db_manager.insert_data(table_name, data)

        self.result_df = pd.concat([self.result_df, result_df], axis=0, ignore_index=True)
        if is_save_file:
            save_df = copy.deepcopy(self.result_df)
            result_grouped = save_df.groupby("Vendor SN")
            for sn, group in result_grouped:
                group = group.reset_index(drop=True)
                slot = re.sub("/", "-", group.loc[0, "槽位"])
                # 确保result目录存在
                os.makedirs("result", exist_ok=True)
                result_file_name = f"result/{sn}_{slot}_{self.start_time}.xlsx"
                with pd.ExcelWriter(result_file_name, engine='openpyxl') as writer:
                    group.to_excel(writer, index=False)

    def get_module_data(self, is_save_file=False):
        """
        直接获取模块数据，不重新执行检测

        Args:
            is_save_file: 是否保存数据到文件/数据库

        Returns:
            获取到的数据
        """
        self._send_command("sys")
        self._send_command("exit")
        self._send_command("display ip-address")
        # 清空响应队列
        while not self.response_queue.empty():
            self.response_queue.get()
        response_lines = ""
        start_time = time.time()
        while time.time() - start_time < 1:
            try:
                response = self.response_queue.get(timeout=0.1)
                if response:
                    response_lines += response
            except Exception as e:
                # 队列超时，继续等待
                continue
        ip_pattern = "\s*NMS\(eth2.2\) ip addr\s*:\s*(.*)\n"
        ip_result = re.findall(ip_pattern, response_lines)
        if len(ip_result) > 0:
            ip = ip_result[0]
        else:
            ip = ""
        slots = range(1, 5)
        slot_result_list = []
        for slot in slots:
            # 清空响应队列
            while not self.response_queue.empty():
                self.response_queue.get()
            # 步骤1进入指定槽位
            self._send_command(f"slot 1/{slot}")
            # 清空响应队列
            while not self.response_queue.empty():
                self.response_queue.get()
            # 查看槽位模块信息
            self._send_command("display cfp2-dco")
            response_lines = ""
            # 等待响应
            start_time = time.time()
            while time.time() - start_time < 1:
                try:
                    response = self.response_queue.get(timeout=0.1)
                    if response:
                        response_lines += response
                except Exception as e:
                    # print(f"错误 {e}")
                    # 队列超时，继续等待
                    continue
            # 使用正则表达式匹配 "There are X CFP2_DCO module online:"
            pattern = r"There are (\d+) CFP2_DCO module online:"
            match = re.search(pattern, response_lines)
            if match:
                module_count = int(match.group(1))
                print(f"检测到 {module_count} 个模块")
                if module_count == 1:
                    # 使用正则表达式匹配 "There are X CFP2_DCO module online:"
                    pattern = r"CFP2_DCO Module No.(\d) information:"
                    match = re.search(pattern, response_lines)
                    if match:
                        module_location = int(match.group(1)) - 1
                        slot_result_list.append((slot - 1, module_location))
                        if is_save_file:
                            self.parse_data(response_lines, ip, f"1/{slot}-{module_location + 1}", is_save_file)
                elif module_count == 2:
                    slot_result_list.append((slot - 1, 0))
                    slot_result_list.append((slot - 1, 1))
                    if is_save_file:
                        self.parse_data(response_lines, ip, f"1/{slot}", is_save_file)

        return slot_result_list

def list_ports():
    """列出所有可用的串口"""
    import serial.tools.list_ports
    ports = list(serial.tools.list_ports.comports())
    if not ports:
        print("未找到可用的串口")
        return None

    print("\n可用的串口:")
    for i, port in enumerate(ports, 1):
        print(f"{i}. {port.device} - {port.description}")

    while True:
        try:
            choice = input("\n请选择串口编号 (q退出): ").strip()
            if choice.lower() == 'q':
                return None
            index = int(choice) - 1
            if 0 <= index < len(ports):
                return ports[index].device
        except ValueError:
            pass
        print("无效的选择，请重试")


def main():
    # 列出可用串口
    port = list_ports()
    if not port:
        return

    # 选择波特率
    baudrates = ['9600', '19200', '38400', '57600', '115200']
    print("\n可用的波特率:")
    for i, baud in enumerate(baudrates, 1):
        print(f"{i}. {baud}")

    while True:
        try:
            choice = input("\n请选择波特率编号 (默认5): ").strip()
            if not choice:
                baudrate = 115200
                break
            index = int(choice) - 1
            if 0 <= index < len(baudrates):
                baudrate = int(baudrates[index])
                break
        except ValueError:
            pass
        print("无效的选择，请重试")

    # 创建并启动串口会话
    cli = SerialCLI()
    if cli.init_serial(port, baudrate):
        print(f"\n正在打开串口 {port}, 波特率 {baudrate}")
        cli.start()
        while True:
            pass


if __name__ == "__main__":
    main()
