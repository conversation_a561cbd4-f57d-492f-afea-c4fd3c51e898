#!/usr/bin/env python3
"""
修复文件编码问题
"""

import os
from pathlib import Path

def create_clean_files():
    """创建干净的文件"""
    
    # 文件内容定义
    files_content = {
        'src/utils/async_utils.py': '''"""
Async utilities module
"""

import asyncio
from typing import Any, Callable, Coroutine


class AsyncTaskManager:
    """Async task manager"""
    
    def __init__(self):
        self._tasks = []
    
    async def run_task(self, coro: Coroutine) -> Any:
        """Run async task"""
        return await coro


def run_async_task(coro: Coroutine) -> Any:
    """Convenient function to run async task"""
    return asyncio.run(coro)
''',
        
        'src/utils/file_utils.py': '''"""
File utilities module
"""

import shutil
from pathlib import Path
from typing import Union


def ensure_directory(path: Union[str, Path]) -> Path:
    """Ensure directory exists"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def backup_file(source: Union[str, Path], backup_dir: Union[str, Path]) -> Path:
    """Backup file"""
    source = Path(source)
    backup_dir = Path(backup_dir)
    ensure_directory(backup_dir)
    
    backup_path = backup_dir / source.name
    shutil.copy2(source, backup_path)
    return backup_path


def get_file_size(path: Union[str, Path]) -> int:
    """Get file size"""
    return Path(path).stat().st_size
''',
        
        'src/core/test_engine.py': '''"""
Test engine module
"""

from config.settings import Settings
from src.utils.logger import LoggerMixin


class TestEngine(LoggerMixin):
    """Test engine main class"""
    
    def __init__(self, settings: Settings, device_manager):
        self.settings = settings
        self.device_manager = device_manager
        self._running = False
    
    def is_running(self) -> bool:
        """Check if running"""
        return self._running
    
    def start_test(self):
        """Start test"""
        self.log_info("Test engine started")
        self._running = True
    
    def stop_test(self):
        """Stop test"""
        self.log_info("Test engine stopped")
        self._running = False
''',
        
        'src/core/device_manager.py': '''"""
Device manager module
"""

from config.settings import Settings
from src.utils.logger import LoggerMixin


class DeviceManager(LoggerMixin):
    """Device manager"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._devices = {}
    
    def connect_all(self):
        """Connect all devices"""
        self.log_info("Connecting all devices")
    
    def disconnect_all(self):
        """Disconnect all devices"""
        self.log_info("Disconnecting all devices")
''',
        
        'src/ui/main_window.py': '''"""
Main window module
"""

from PySide6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel

from config.settings import Settings
from src.utils.logger import LoggerMixin


class MainWindow(QMainWindow, LoggerMixin):
    """Application main window"""
    
    def __init__(self, settings: Settings, test_engine, device_manager):
        super().__init__()
        
        self.settings = settings
        self.test_engine = test_engine
        self.device_manager = device_manager
        
        self._setup_ui()
        self._load_layout()
    
    def _setup_ui(self):
        """Setup user interface"""
        self.setWindowTitle("OCT Thermal Test V5")
        self.setMinimumSize(800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add placeholder label
        label = QLabel("OCT Thermal Test V5\\n\\nProject architecture created!\\n\\nPlease continue implementing specific features...")
        label.setStyleSheet("font-size: 16px; text-align: center; padding: 50px;")
        layout.addWidget(label)
    
    def _load_layout(self):
        """Load window layout"""
        if self.settings.ui.window_size:
            self.resize(*self.settings.ui.window_size)
        
        if self.settings.ui.window_position:
            self.move(*self.settings.ui.window_position)
    
    def save_layout(self):
        """Save window layout"""
        self.log_info("Save window layout")
'''
    }
    
    # 删除有问题的文件并重新创建
    for file_path, content in files_content.items():
        full_path = Path(file_path)
        
        # 删除现有文件
        if full_path.exists():
            full_path.unlink()
            print(f"Deleted: {file_path}")
        
        # 确保目录存在
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建新文件
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        
        print(f"Created: {file_path}")
    
    print("All files fixed!")

if __name__ == "__main__":
    create_clean_files()
