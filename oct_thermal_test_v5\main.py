#!/usr/bin/env python3
"""
OCT Thermal Test V5 主入口文件
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("QT_API", "pyside6")

def main():
    """主函数"""
    try:
        from src.app.application import main as app_main
        return app_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
