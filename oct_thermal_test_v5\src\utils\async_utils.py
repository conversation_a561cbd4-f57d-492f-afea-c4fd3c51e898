"""
Async utilities module
"""

import async<PERSON>
from typing import Any, Callable, Coroutine


class AsyncTaskManager:
    """Async task manager"""
    
    def __init__(self):
        self._tasks = []
    
    async def run_task(self, coro: Coroutine) -> Any:
        """Run async task"""
        return await coro


def run_async_task(coro: Coroutine) -> Any:
    """Convenient function to run async task"""
    return asyncio.run(coro)