"""
Test engine module
"""

from config.settings import Settings
from src.utils.logger import LoggerMixin


class TestEngine(LoggerMixin):
    """Test engine main class"""
    
    def __init__(self, settings: Settings, device_manager):
        self.settings = settings
        self.device_manager = device_manager
        self._running = False
    
    def is_running(self) -> bool:
        """Check if running"""
        return self._running
    
    def start_test(self):
        """Start test"""
        self.log_info("Test engine started")
        self._running = True
    
    def stop_test(self):
        """Stop test"""
        self.log_info("Test engine stopped")
        self._running = False