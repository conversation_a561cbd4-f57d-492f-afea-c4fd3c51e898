"""
测试配置对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QSpinBox, QCheckBox, QPushButton, QGroupBox,
    QLineEdit, QTextEdit, QComboBox, QMessageBox
)
from PySide6.QtCore import Qt, Signal

from config.settings import Settings
from src.utils.logger import LoggerMixin


class TestConfigDialog(QDialog, LoggerMixin):
    """测试配置对话框"""
    
    config_changed = Signal(dict)
    
    def __init__(self, settings: Settings, parent=None):
        super().__init__(parent)
        
        self.settings = settings
        self._setup_ui()
        self._load_config()
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("测试配置")
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 基本测试参数
        basic_group = QGroupBox("基本测试参数")
        basic_layout = QFormLayout(basic_group)
        
        self.loop_interval_spin = QSpinBox()
        self.loop_interval_spin.setRange(1, 3600)
        self.loop_interval_spin.setSuffix(" 秒")
        
        self.stability_timeout_spin = QSpinBox()
        self.stability_timeout_spin.setRange(60, 1800)
        self.stability_timeout_spin.setSuffix(" 秒")
        
        self.max_workers_spin = QSpinBox()
        self.max_workers_spin.setRange(1, 16)
        
        basic_layout.addRow("循环间隔:", self.loop_interval_spin)
        basic_layout.addRow("稳定性超时:", self.stability_timeout_spin)
        basic_layout.addRow("最大工作线程:", self.max_workers_spin)
        
        layout.addWidget(basic_group)
        
        # 测试选项
        options_group = QGroupBox("测试选项")
        options_layout = QVBoxLayout(options_group)
        
        self.auto_clear_errors_check = QCheckBox("自动清零错误计数")
        self.auto_generate_reports_check = QCheckBox("自动生成报告")
        self.enable_logging_check = QCheckBox("启用详细日志")
        
        options_layout.addWidget(self.auto_clear_errors_check)
        options_layout.addWidget(self.auto_generate_reports_check)
        options_layout.addWidget(self.enable_logging_check)
        
        layout.addWidget(options_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self._save_config)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self._reset_defaults)
        
        button_layout.addWidget(reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _load_config(self):
        """加载配置"""
        self.loop_interval_spin.setValue(self.settings.test.loop_interval)
        self.stability_timeout_spin.setValue(self.settings.test.stability_timeout)
        self.max_workers_spin.setValue(self.settings.test.max_workers)
        
        self.auto_clear_errors_check.setChecked(self.settings.test.auto_clear_errors)
        self.auto_generate_reports_check.setChecked(self.settings.test.auto_generate_reports)
        self.enable_logging_check.setChecked(self.settings.test.enable_detailed_logging)
    
    def _save_config(self):
        """保存配置"""
        try:
            # 更新设置
            self.settings.test.loop_interval = self.loop_interval_spin.value()
            self.settings.test.stability_timeout = self.stability_timeout_spin.value()
            self.settings.test.max_workers = self.max_workers_spin.value()
            
            self.settings.test.auto_clear_errors = self.auto_clear_errors_check.isChecked()
            self.settings.test.auto_generate_reports = self.auto_generate_reports_check.isChecked()
            self.settings.test.enable_detailed_logging = self.enable_logging_check.isChecked()
            
            # 发射配置变化信号
            config = self.get_config()
            self.config_changed.emit(config)
            
            QMessageBox.information(self, "成功", "测试配置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败:\n{e}")
    
    def _reset_defaults(self):
        """重置为默认值"""
        self.loop_interval_spin.setValue(10)
        self.stability_timeout_spin.setValue(300)
        self.max_workers_spin.setValue(4)
        
        self.auto_clear_errors_check.setChecked(True)
        self.auto_generate_reports_check.setChecked(True)
        self.enable_logging_check.setChecked(False)
    
    def get_config(self) -> dict:
        """获取配置"""
        return {
            "loop_interval": self.loop_interval_spin.value(),
            "stability_timeout": self.stability_timeout_spin.value(),
            "max_workers": self.max_workers_spin.value(),
            "auto_clear_errors": self.auto_clear_errors_check.isChecked(),
            "auto_generate_reports": self.auto_generate_reports_check.isChecked(),
            "enable_detailed_logging": self.enable_logging_check.isChecked()
        }
