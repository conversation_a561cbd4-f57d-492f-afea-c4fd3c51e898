"""
报告生成器
提供Excel报告生成、数据导出和格式化功能
"""

from __future__ import annotations
import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill

from service.common.my_logger import logger
from service.common.const import (
    BASE_OUTPUT_FILE_PATH,
    EXCEL_NAME_TIME_FORMAT,
    STANDARD_DATETIME_FORMAT,
    OUTPUT_KEY_LIST,
    ERROR_KEY_LISY,
    RESULT_STR_MAPPER
)
from service.core.data_collector import DataPoint
from service.core.validation_engine import ValidationReport, ValidationError
from service.core.config_manager import ConfigManager


@dataclass
class ReportConfig:
    """报告配置"""
    output_path: str
    file_name_template: str
    include_charts: bool = False
    auto_adjust_columns: bool = True
    highlight_errors: bool = True
    
    # 样式配置
    header_font_size: int = 12
    header_font_bold: bool = True
    error_bg_color: str = "FFD6D6"
    pass_bg_color: str = "DFFFD6"
    warning_bg_color: str = "FFF2CC"


class ExcelReportGenerator:
    """Excel报告生成器"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self._ensure_output_directory()
    
    def _ensure_output_directory(self) -> None:
        """确保输出目录存在"""
        if not os.path.exists(self.config.output_path):
            os.makedirs(self.config.output_path, exist_ok=True)
    
    def generate_module_report(self, module_id: Union[int, str], sn: str,
                             data_points: List[DataPoint],
                             validation_report: ValidationReport,
                             test_start_time: datetime) -> str:
        """生成单个模块的报告"""
        # 生成文件名
        time_str = test_start_time.strftime(EXCEL_NAME_TIME_FORMAT)
        file_name = f"{time_str}__{sn}.xlsx"
        file_path = os.path.join(self.config.output_path, file_name)
        
        try:
            # 创建工作簿
            wb = Workbook()
            
            # 生成数据表
            self._create_data_sheet(wb, data_points, module_id)
            
            # 生成错误表
            self._create_error_sheet(wb, validation_report.errors)
            
            # 生成摘要表
            self._create_summary_sheet(wb, validation_report, sn)
            
            # 保存文件
            wb.save(file_path)
            
            logger.info(f"模块报告生成成功: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"生成模块报告失败: {e}")
            raise
    
    def _create_data_sheet(self, wb: Workbook, data_points: List[DataPoint], 
                          module_id: Union[int, str]) -> None:
        """创建数据表"""
        # 移除默认工作表并创建数据表
        if "Sheet" in wb.sheetnames:
            wb.remove(wb["Sheet"])
        
        ws = wb.create_sheet("data")
        
        # 过滤模块数据
        module_data = [dp for dp in data_points if dp.module_id == module_id]
        
        if not module_data:
            return
        
        # 按时间戳分组数据
        time_groups = {}
        for dp in module_data:
            time_key = dp.timestamp.strftime(STANDARD_DATETIME_FORMAT)
            if time_key not in time_groups:
                time_groups[time_key] = {}
            time_groups[time_key][dp.parameter] = dp.value
        
        # 创建DataFrame
        df_data = []
        for time_key, params in time_groups.items():
            row = {"datetime": time_key}
            row.update(params)
            df_data.append(row)
        
        if df_data:
            df = pd.DataFrame(df_data)
            
            # 确保包含所有需要的列
            for col in OUTPUT_KEY_LIST:
                if col not in df.columns:
                    df[col] = None
            
            # 重新排序列
            available_cols = [col for col in OUTPUT_KEY_LIST if col in df.columns]
            df = df[available_cols]
            
            # 写入Excel
            for r_idx, (_, row) in enumerate(df.iterrows(), 1):
                for c_idx, (col_name, value) in enumerate(row.items(), 1):
                    if r_idx == 1:
                        # 写入表头
                        cell = ws.cell(row=1, column=c_idx, value=col_name)
                        cell.font = Font(bold=self.config.header_font_bold, 
                                       size=self.config.header_font_size)
                        cell.alignment = Alignment(horizontal="center")
                    
                    # 写入数据
                    ws.cell(row=r_idx + 1, column=c_idx, value=value)
        
        # 自动调整列宽
        if self.config.auto_adjust_columns:
            self._auto_adjust_columns(ws)
    
    def _create_error_sheet(self, wb: Workbook, errors: List[ValidationError]) -> None:
        """创建错误表"""
        ws = wb.create_sheet("error")
        
        if not errors:
            ws.cell(row=1, column=1, value="无验证错误")
            return
        
        # 创建错误数据
        error_data = []
        for error in errors:
            error_data.append({
                "datetime": error.timestamp.strftime(STANDARD_DATETIME_FORMAT),
                "name": error.parameter,
                "value": error.value,
                "min_value": error.expected_min,
                "max_value": error.expected_max,
                "is_pass": False,
                "error_message": error.message
            })
        
        df = pd.DataFrame(error_data)
        
        # 确保包含所有错误列
        for col in ERROR_KEY_LISY:
            if col not in df.columns:
                df[col] = None
        
        # 重新排序列
        available_cols = [col for col in ERROR_KEY_LISY if col in df.columns]
        if "error_message" in df.columns and "error_message" not in available_cols:
            available_cols.append("error_message")
        
        df = df[available_cols]
        
        # 写入Excel
        for r_idx, (_, row) in enumerate(df.iterrows(), 1):
            for c_idx, (col_name, value) in enumerate(row.items(), 1):
                if r_idx == 1:
                    # 写入表头
                    cell = ws.cell(row=1, column=c_idx, value=col_name)
                    cell.font = Font(bold=self.config.header_font_bold,
                                   size=self.config.header_font_size)
                    cell.alignment = Alignment(horizontal="center")
                
                # 写入数据
                cell = ws.cell(row=r_idx + 1, column=c_idx, value=value)
                
                # 错误行高亮
                if self.config.highlight_errors:
                    cell.fill = PatternFill(start_color=self.config.error_bg_color,
                                          end_color=self.config.error_bg_color,
                                          fill_type="solid")
        
        # 自动调整列宽
        if self.config.auto_adjust_columns:
            self._auto_adjust_columns(ws)
    
    def _create_summary_sheet(self, wb: Workbook, validation_report: ValidationReport, 
                            sn: str) -> None:
        """创建摘要表"""
        ws = wb.create_sheet("summary", 0)  # 插入到第一个位置
        
        # 测试结果摘要
        result_text = RESULT_STR_MAPPER.get(validation_report.is_pass, "未知状态")
        
        # 合并单元格显示结果
        ws.merge_cells('A1:F1')
        result_cell = ws['A1']
        result_cell.value = f"测试结果: {result_text} (SN: {sn})"
        result_cell.font = Font(bold=True, size=14,
                              color="00FF00" if validation_report.is_pass else "FF0000")
        result_cell.alignment = Alignment(horizontal="center", vertical="center")
        result_cell.fill = PatternFill(
            start_color=self.config.pass_bg_color if validation_report.is_pass else self.config.error_bg_color,
            end_color=self.config.pass_bg_color if validation_report.is_pass else self.config.error_bg_color,
            fill_type="solid"
        )
        
        # 统计信息
        stats_data = [
            ["测试时间", validation_report.timestamp.strftime(STANDARD_DATETIME_FORMAT)],
            ["模块ID", str(validation_report.module_id)],
            ["序列号", sn],
            ["总检查项", validation_report.total_checks],
            ["通过项", validation_report.passed_checks],
            ["失败项", validation_report.failed_checks],
            ["警告项", validation_report.warning_checks],
            ["跳过项", validation_report.skipped_checks],
            ["通过率", f"{validation_report.passed_checks / max(validation_report.total_checks, 1) * 100:.1f}%"]
        ]
        
        # 写入统计信息
        for i, (label, value) in enumerate(stats_data, 3):
            ws.cell(row=i, column=1, value=label).font = Font(bold=True)
            ws.cell(row=i, column=2, value=value)
        
        # 自动调整列宽
        if self.config.auto_adjust_columns:
            self._auto_adjust_columns(ws)
    
    def _auto_adjust_columns(self, worksheet) -> None:
        """自动调整列宽"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)  # 最大宽度50
            worksheet.column_dimensions[column_letter].width = adjusted_width


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        
        # 创建报告配置
        self.report_config = ReportConfig(
            output_path=config_manager.get_output_path(),
            file_name_template="{time}__{sn}.xlsx",
            include_charts=False,
            auto_adjust_columns=True,
            highlight_errors=True
        )
        
        # 初始化生成器
        self.excel_generator = ExcelReportGenerator(self.report_config)
        
        # 报告历史
        self._generated_reports: List[Dict[str, Any]] = []
    
    def generate_module_reports(self, test_start_time: datetime,
                              all_data_points: List[DataPoint],
                              validation_reports: Dict[Union[int, str], ValidationReport],
                              module_sn_mapping: Dict[Union[int, str], str]) -> Dict[Union[int, str], str]:
        """生成所有模块的报告"""
        report_paths = {}
        
        for module_id, validation_report in validation_reports.items():
            try:
                sn = module_sn_mapping.get(module_id, f"UNKNOWN_{module_id}")
                
                # 过滤模块数据
                module_data = [dp for dp in all_data_points if dp.module_id == module_id]
                
                # 生成报告
                report_path = self.excel_generator.generate_module_report(
                    module_id=module_id,
                    sn=sn,
                    data_points=module_data,
                    validation_report=validation_report,
                    test_start_time=test_start_time
                )
                
                report_paths[module_id] = report_path
                
                # 记录报告信息
                self._generated_reports.append({
                    "module_id": module_id,
                    "sn": sn,
                    "file_path": report_path,
                    "generation_time": datetime.now(),
                    "test_result": validation_report.is_pass,
                    "total_checks": validation_report.total_checks,
                    "failed_checks": validation_report.failed_checks
                })
                
            except Exception as e:
                logger.error(f"生成模块 {module_id} 报告失败: {e}")
                continue
        
        logger.info(f"生成了 {len(report_paths)} 个模块报告")
        return report_paths
    
    def generate_summary_report(self, test_start_time: datetime,
                              validation_reports: Dict[Union[int, str], ValidationReport],
                              module_sn_mapping: Dict[Union[int, str], str]) -> str:
        """生成汇总报告"""
        time_str = test_start_time.strftime(EXCEL_NAME_TIME_FORMAT)
        file_name = f"{time_str}__SUMMARY.xlsx"
        file_path = os.path.join(self.report_config.output_path, file_name)
        
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "测试汇总"
            
            # 表头
            headers = ["模块ID", "序列号", "测试结果", "总检查项", "通过项", "失败项", "通过率", "测试时间"]
            for i, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=i, value=header)
                cell.font = Font(bold=True, size=12)
                cell.alignment = Alignment(horizontal="center")
            
            # 数据行
            for row_idx, (module_id, report) in enumerate(validation_reports.items(), 2):
                sn = module_sn_mapping.get(module_id, f"UNKNOWN_{module_id}")
                pass_rate = report.passed_checks / max(report.total_checks, 1) * 100
                
                data = [
                    str(module_id),
                    sn,
                    "通过" if report.is_pass else "失败",
                    report.total_checks,
                    report.passed_checks,
                    report.failed_checks,
                    f"{pass_rate:.1f}%",
                    report.timestamp.strftime(STANDARD_DATETIME_FORMAT)
                ]
                
                for col_idx, value in enumerate(data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    
                    # 根据测试结果设置背景色
                    if col_idx == 3:  # 测试结果列
                        if report.is_pass:
                            cell.fill = PatternFill(start_color=self.report_config.pass_bg_color,
                                                  end_color=self.report_config.pass_bg_color,
                                                  fill_type="solid")
                        else:
                            cell.fill = PatternFill(start_color=self.report_config.error_bg_color,
                                                  end_color=self.report_config.error_bg_color,
                                                  fill_type="solid")
            
            # 自动调整列宽
            self.excel_generator._auto_adjust_columns(ws)
            
            # 保存文件
            wb.save(file_path)
            
            logger.info(f"汇总报告生成成功: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")
            raise
    
    def export_data_to_csv(self, data_points: List[DataPoint], 
                          file_path: str) -> None:
        """导出数据到CSV文件"""
        try:
            # 转换为DataFrame
            data_list = []
            for dp in data_points:
                data_list.append({
                    "timestamp": dp.timestamp.strftime(STANDARD_DATETIME_FORMAT),
                    "source": dp.source,
                    "module_id": dp.module_id,
                    "parameter": dp.parameter,
                    "value": dp.value,
                    "unit": dp.unit,
                    "status": dp.status
                })
            
            df = pd.DataFrame(data_list)
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            logger.info(f"数据导出到CSV成功: {file_path}")
            
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            raise
    
    def get_report_statistics(self) -> Dict[str, Any]:
        """获取报告统计信息"""
        if not self._generated_reports:
            return {"total_reports": 0}
        
        total_reports = len(self._generated_reports)
        passed_reports = sum(1 for r in self._generated_reports if r["test_result"])
        failed_reports = total_reports - passed_reports
        
        return {
            "total_reports": total_reports,
            "passed_reports": passed_reports,
            "failed_reports": failed_reports,
            "pass_rate": passed_reports / total_reports * 100 if total_reports > 0 else 0,
            "latest_report_time": max(r["generation_time"] for r in self._generated_reports) if self._generated_reports else None
        }
    
    def cleanup_old_reports(self, days_to_keep: int = 30) -> None:
        """清理旧报告文件"""
        try:
            import glob
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            pattern = os.path.join(self.report_config.output_path, "*.xlsx")
            
            deleted_count = 0
            for file_path in glob.glob(pattern):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    os.remove(file_path)
                    deleted_count += 1
            
            logger.info(f"清理了 {deleted_count} 个旧报告文件")
            
        except Exception as e:
            logger.error(f"清理旧报告失败: {e}")
