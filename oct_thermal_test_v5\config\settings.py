"""
应用程序设置管理
基于Pydantic的类型安全配置管理
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator


class DeviceConnectionConfig(BaseModel):
    """设备连接配置"""
    host: str
    port: int
    username: str = "admin"
    password: str = "Admin_123"
    timeout: int = 30
    retry_count: int = 3
    retry_delay: float = 1.0


class DataFieldConfig(BaseModel):
    """数据字段配置"""
    name: str  # 字段名称
    display_name: str  # 显示名称
    data_type: str = Field(default="float", pattern="^(int|float|string|hex)$")
    unit: Optional[str] = None
    format_string: Optional[str] = None  # 格式化字符串，如 "{:.2f}"
    parser_function: Optional[str] = None  # 解析函数名
    enabled: bool = True


class OCTDeviceConfig(BaseModel):
    """OCT设备配置"""
    device_id: str  # 设备唯一标识
    name: str  # 设备名称
    connection_type: str = Field(pattern="^(ssh|serial)$")
    host: str  # IP地址或串口号
    port: int = 22
    username: str = "admin"
    password: str = "Admin_123"
    timeout: int = 30
    retry_count: int = 3
    retry_delay: float = 1.0

    # 设备特定配置
    slots: List[Union[int, str]] = Field(default_factory=list)
    device_type: str = "cfp2_dco"  # 设备类型，用于选择解析器
    data_fields: List[DataFieldConfig] = Field(default_factory=list)  # 数据字段配置

    # 串口特定配置
    baudrate: int = 9600
    bytesize: int = 8
    parity: str = "N"
    stopbits: int = 1

    enabled: bool = True


class NPBDeviceConfig(BaseModel):
    """NPB设备配置"""
    device_id: str  # 设备唯一标识
    name: str  # 设备名称
    host: str
    port: int = 80
    username: str = "admin"
    password: str = "Admin_123"
    timeout: int = 30
    retry_count: int = 3

    # NPB特定配置
    ports: List[str] = Field(default_factory=list)  # 端口列表
    device_type: str = "standard_npb"  # 设备类型
    api_version: str = "v1"  # API版本

    enabled: bool = True


class MESConfig(BaseModel):
    """MES系统配置"""
    enabled: bool = False
    base_url: str = ""
    username: str = ""
    password: str = ""
    timeout: int = 30


class ValidationRule(BaseModel):
    """验证规则配置"""
    name: str
    parameter: str
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    value_type: str = Field(default="float", pattern="^(int|float|string)$")
    rule_type: str = Field(default="range", pattern="^(range|increase|custom)$")
    enabled: bool = True
    unit: Optional[str] = None
    description: Optional[str] = None
    
    # 特殊规则配置
    sn_specific: bool = False
    target_sn: Optional[str] = None
    dynamic_threshold: bool = False


class TestConfig(BaseModel):
    """测试配置"""
    loop_interval: int = Field(default=10, ge=1, le=3600)
    stability_timeout: int = Field(default=300, ge=60, le=1800)
    stability_check_interval: int = Field(default=10, ge=5, le=60)
    post_stability_wait_time: int = Field(default=120, ge=0, le=600)
    max_workers: int = Field(default=4, ge=1, le=16)
    task_timeout: int = Field(default=60, ge=10, le=300)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1, le=10.0)
    
    # 验证规则
    validation_rules: List[ValidationRule] = Field(default_factory=list)
    
    # 报告配置
    output_path: str = "./output"
    auto_generate_reports: bool = True
    report_format: str = Field(default="excel", pattern="^(excel|csv|json)$")


class UIConfig(BaseModel):
    """UI配置"""
    theme: str = Field(default="light", pattern="^(light|dark|auto)$")
    language: str = Field(default="zh_CN", pattern="^(zh_CN|en_US)$")
    window_size: tuple[int, int] = (1200, 800)
    window_position: Optional[tuple[int, int]] = None
    auto_save_layout: bool = True
    refresh_interval: int = Field(default=1000, ge=100, le=10000)


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$")
    file_enabled: bool = True
    file_path: str = "./logs"
    file_rotation: str = "1 day"
    file_retention: str = "30 days"
    console_enabled: bool = True
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"


class Settings(BaseModel):
    """应用程序主配置"""
    
    # 应用信息
    app_name: str = "OCT Thermal Test V5"
    app_version: str = "5.0.0"
    app_description: str = "OCT L-Band O2 温循测试上位机系统"
    
    # 设备配置
    oct_devices: List[OCTDeviceConfig] = Field(default_factory=list)
    npb_devices: List[NPBDeviceConfig] = Field(default_factory=list)
    mes: MESConfig = Field(default_factory=MESConfig)
    
    # 测试配置
    test: TestConfig = Field(default_factory=TestConfig)
    
    # UI配置
    ui: UIConfig = Field(default_factory=UIConfig)
    
    # 日志配置
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # 高级配置
    debug_mode: bool = False
    performance_monitoring: bool = False
    auto_backup: bool = True
    backup_interval: int = Field(default=3600, ge=300, le=86400)  # 秒
    
    class Config:
        # 移除pydantic_settings相关配置
        pass
        
    @classmethod
    def load_from_file(cls, config_path: Union[str, Path]) -> "Settings":
        """从JSON文件加载配置"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            # 如果配置文件不存在，创建默认配置
            default_settings = cls()
            default_settings.save_to_file(config_path)
            return default_settings
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        except Exception as e:
            raise ValueError(f"Failed to load config from {config_path}: {e}")
    
    def save_to_file(self, config_path: Union[str, Path]) -> None:
        """保存配置到JSON文件"""
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(
                    self.dict(),
                    f,
                    ensure_ascii=False,
                    indent=4,
                    default=str
                )
        except Exception as e:
            raise ValueError(f"Failed to save config to {config_path}: {e}")
    
    def update_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_device_config_list(self) -> List[Dict[str, Any]]:
        """获取设备配置列表（兼容原有格式）"""
        config_list = []
        
        for i, slot in enumerate(self.oct_device.slots):
            # 为每个slot创建配置项
            for j, npb_host in enumerate(self.npb_device.hosts):
                config_item = {
                    "oct_slot": slot,
                    "npb_ip": npb_host,
                    "npb_data_port": f"port_{i}_{j}",  # 示例端口命名
                }
                config_list.append(config_item)
        
        return config_list
    
    def validate_configuration(self) -> List[str]:
        """验证配置的有效性"""
        errors = []

        # 验证OCT设备配置
        for i, oct_device in enumerate(self.oct_devices):
            if not oct_device.host:
                errors.append(f"OCT device {i+1} host is required")

            if not oct_device.slots:
                errors.append(f"OCT device {i+1} slots cannot be empty")

        # 验证NPB设备配置
        for i, npb_device in enumerate(self.npb_devices):
            if not npb_device.host:
                errors.append(f"NPB device {i+1} host is required")

        # 验证测试配置
        if self.test.loop_interval <= 0:
            errors.append("Test loop interval must be positive")

        # 验证输出路径
        try:
            Path(self.test.output_path).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            errors.append(f"Invalid output path: {e}")

        return errors


# 全局设置实例
_settings: Optional[Settings] = None


def get_settings(config_path: Optional[Union[str, Path]] = None) -> Settings:
    """获取全局设置实例"""
    global _settings
    
    if _settings is None:
        if config_path is None:
            # 默认配置文件路径
            config_path = Path(__file__).parent / "default.json"
        
        _settings = Settings.load_from_file(config_path)
    
    return _settings


def reload_settings(config_path: Union[str, Path]) -> Settings:
    """重新加载设置"""
    global _settings
    _settings = Settings.load_from_file(config_path)
    return _settings
