"""
设备配置对话框
支持多OCT设备和多NPB设备配置
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QWidget, QFormLayout, QLineEdit, QSpinBox,
    QComboBox, QCheckBox, QPushButton, QTableWidget,
    QTableWidgetItem, QMessageBox, QTextEdit,
    QGroupBox, QLabel
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from config.settings import Settings, OCTDeviceConfig, NPBDeviceConfig, DataFieldConfig
from src.utils.logger import LoggerMixin


class DeviceConfigDialog(QDialog, LoggerMixin):
    """设备配置对话框"""
    
    config_changed = Signal(dict)
    
    def __init__(self, settings: Settings, parent=None):
        super().__init__(parent)
        
        self.settings = settings
        self.oct_devices = []
        self.npb_devices = []
        
        self._setup_ui()
        self._load_config()
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("设备配置")
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # OCT设备配置标签页
        oct_tab = self._create_oct_config_tab()
        tab_widget.addTab(oct_tab, "OCT设备")
        
        # NPB设备配置标签页
        npb_tab = self._create_npb_config_tab()
        tab_widget.addTab(npb_tab, "NPB设备")
        
        layout.addWidget(tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self._save_config)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        test_btn = QPushButton("测试连接")
        test_btn.clicked.connect(self._test_connections)
        
        button_layout.addWidget(test_btn)
        button_layout.addStretch()
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _create_oct_config_tab(self):
        """创建OCT设备配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 设备列表
        devices_group = QGroupBox("OCT设备列表")
        devices_layout = QVBoxLayout(devices_group)
        
        # 添加设备按钮
        add_oct_btn = QPushButton("添加OCT设备")
        add_oct_btn.clicked.connect(self._add_oct_device)
        devices_layout.addWidget(add_oct_btn)
        
        # 设备表格
        self.oct_table = QTableWidget()
        self.oct_table.setColumnCount(7)
        self.oct_table.setHorizontalHeaderLabels([
            "设备ID", "名称", "连接类型", "地址", "端口", "启用", "操作"
        ])
        devices_layout.addWidget(self.oct_table)
        
        layout.addWidget(devices_group)
        
        # 设备详细配置
        detail_group = QGroupBox("设备详细配置")
        detail_layout = QFormLayout(detail_group)
        
        self.oct_device_id_edit = QLineEdit()
        self.oct_name_edit = QLineEdit()
        self.oct_type_combo = QComboBox()
        self.oct_type_combo.addItems(["ssh", "serial"])
        self.oct_host_edit = QLineEdit()
        self.oct_port_spin = QSpinBox()
        self.oct_port_spin.setRange(1, 65535)
        self.oct_username_edit = QLineEdit()
        self.oct_password_edit = QLineEdit()
        self.oct_password_edit.setEchoMode(QLineEdit.Password)
        
        detail_layout.addRow("设备ID:", self.oct_device_id_edit)
        detail_layout.addRow("设备名称:", self.oct_name_edit)
        detail_layout.addRow("连接类型:", self.oct_type_combo)
        detail_layout.addRow("地址/串口:", self.oct_host_edit)
        detail_layout.addRow("端口/波特率:", self.oct_port_spin)
        detail_layout.addRow("用户名:", self.oct_username_edit)
        detail_layout.addRow("密码:", self.oct_password_edit)
        
        # 插槽配置
        slots_label = QLabel("插槽配置 (逗号分隔):")
        self.oct_slots_edit = QLineEdit()
        self.oct_slots_edit.setPlaceholderText("例如: 1,2,3,4")
        detail_layout.addRow(slots_label, self.oct_slots_edit)
        
        layout.addWidget(detail_group)
        
        return widget
    
    def _create_npb_config_tab(self):
        """创建NPB设备配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 设备列表
        devices_group = QGroupBox("NPB设备列表")
        devices_layout = QVBoxLayout(devices_group)
        
        # 添加设备按钮
        add_npb_btn = QPushButton("添加NPB设备")
        add_npb_btn.clicked.connect(self._add_npb_device)
        devices_layout.addWidget(add_npb_btn)
        
        # 设备表格
        self.npb_table = QTableWidget()
        self.npb_table.setColumnCount(6)
        self.npb_table.setHorizontalHeaderLabels([
            "设备ID", "名称", "地址", "端口", "启用", "操作"
        ])
        devices_layout.addWidget(self.npb_table)
        
        layout.addWidget(devices_group)
        
        # 设备详细配置
        detail_group = QGroupBox("设备详细配置")
        detail_layout = QFormLayout(detail_group)
        
        self.npb_device_id_edit = QLineEdit()
        self.npb_name_edit = QLineEdit()
        self.npb_host_edit = QLineEdit()
        self.npb_port_spin = QSpinBox()
        self.npb_port_spin.setRange(1, 65535)
        self.npb_port_spin.setValue(80)
        self.npb_username_edit = QLineEdit()
        self.npb_password_edit = QLineEdit()
        self.npb_password_edit.setEchoMode(QLineEdit.Password)
        
        detail_layout.addRow("设备ID:", self.npb_device_id_edit)
        detail_layout.addRow("设备名称:", self.npb_name_edit)
        detail_layout.addRow("IP地址:", self.npb_host_edit)
        detail_layout.addRow("端口:", self.npb_port_spin)
        detail_layout.addRow("用户名:", self.npb_username_edit)
        detail_layout.addRow("密码:", self.npb_password_edit)
        
        # 端口配置
        ports_label = QLabel("端口配置 (逗号分隔):")
        self.npb_ports_edit = QLineEdit()
        self.npb_ports_edit.setPlaceholderText("例如: 1,2,3,4")
        detail_layout.addRow(ports_label, self.npb_ports_edit)
        
        layout.addWidget(detail_group)
        
        return widget
    
    def _load_config(self):
        """加载配置"""
        # 加载OCT设备
        self.oct_devices = self.settings.oct_devices.copy()
        self._refresh_oct_table()
        
        # 加载NPB设备
        self.npb_devices = self.settings.npb_devices.copy()
        self._refresh_npb_table()
    
    def _refresh_oct_table(self):
        """刷新OCT设备表格"""
        self.oct_table.setRowCount(len(self.oct_devices))
        
        for row, device in enumerate(self.oct_devices):
            self.oct_table.setItem(row, 0, QTableWidgetItem(device.device_id))
            self.oct_table.setItem(row, 1, QTableWidgetItem(device.name))
            self.oct_table.setItem(row, 2, QTableWidgetItem(device.connection_type))
            self.oct_table.setItem(row, 3, QTableWidgetItem(device.host))
            self.oct_table.setItem(row, 4, QTableWidgetItem(str(device.port)))
            
            # 启用状态
            enabled_item = QTableWidgetItem("是" if device.enabled else "否")
            self.oct_table.setItem(row, 5, enabled_item)
            
            # 操作按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, r=row: self._delete_oct_device(r))
            self.oct_table.setCellWidget(row, 6, delete_btn)
    
    def _refresh_npb_table(self):
        """刷新NPB设备表格"""
        self.npb_table.setRowCount(len(self.npb_devices))
        
        for row, device in enumerate(self.npb_devices):
            self.npb_table.setItem(row, 0, QTableWidgetItem(device.device_id))
            self.npb_table.setItem(row, 1, QTableWidgetItem(device.name))
            self.npb_table.setItem(row, 2, QTableWidgetItem(device.host))
            self.npb_table.setItem(row, 3, QTableWidgetItem(str(device.port)))
            
            # 启用状态
            enabled_item = QTableWidgetItem("是" if device.enabled else "否")
            self.npb_table.setItem(row, 4, enabled_item)
            
            # 操作按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, r=row: self._delete_npb_device(r))
            self.npb_table.setCellWidget(row, 5, delete_btn)
    
    def _add_oct_device(self):
        """添加OCT设备"""
        device_id = f"oct_{len(self.oct_devices) + 1}"
        device = OCTDeviceConfig(
            device_id=device_id,
            name=f"OCT设备{len(self.oct_devices) + 1}",
            connection_type="ssh",
            host="*************",
            port=22,
            username="admin",
            password="Admin_123",
            slots=[1, 2, 3, 4],
            enabled=True
        )
        
        self.oct_devices.append(device)
        self._refresh_oct_table()
        self.log_info(f"添加OCT设备: {device_id}")
    
    def _add_npb_device(self):
        """添加NPB设备"""
        device_id = f"npb_{len(self.npb_devices) + 1}"
        device = NPBDeviceConfig(
            device_id=device_id,
            name=f"NPB设备{len(self.npb_devices) + 1}",
            host="*************",
            port=80,
            username="admin",
            password="Admin_123",
            ports=["1", "2"],
            enabled=True
        )
        
        self.npb_devices.append(device)
        self._refresh_npb_table()
        self.log_info(f"添加NPB设备: {device_id}")
    
    def _delete_oct_device(self, row: int):
        """删除OCT设备"""
        if 0 <= row < len(self.oct_devices):
            device_id = self.oct_devices[row].device_id
            del self.oct_devices[row]
            self._refresh_oct_table()
            self.log_info(f"删除OCT设备: {device_id}")
    
    def _delete_npb_device(self, row: int):
        """删除NPB设备"""
        if 0 <= row < len(self.npb_devices):
            device_id = self.npb_devices[row].device_id
            del self.npb_devices[row]
            self._refresh_npb_table()
            self.log_info(f"删除NPB设备: {device_id}")
    
    def _save_config(self):
        """保存配置"""
        try:
            # 更新设置
            self.settings.oct_devices = self.oct_devices
            self.settings.npb_devices = self.npb_devices
            
            # 发射配置变化信号
            config = {
                "oct_devices": [device.dict() for device in self.oct_devices],
                "npb_devices": [device.dict() for device in self.npb_devices]
            }
            self.config_changed.emit(config)
            
            QMessageBox.information(self, "成功", "设备配置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败:\n{e}")
    
    def _test_connections(self):
        """测试设备连接"""
        QMessageBox.information(self, "测试连接", "连接测试功能正在开发中...")
    
    def get_config(self) -> dict:
        """获取配置"""
        return {
            "oct_devices": [device.dict() for device in self.oct_devices],
            "npb_devices": [device.dict() for device in self.npb_devices]
        }
