"""
数据采集器
统一数据采集接口，支持多源数据采集和格式标准化
"""

from __future__ import annotations
import asyncio
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod
from collections import defaultdict

from service.common.my_logger import logger
from service.common.const import STANDARD_DATETIME_FORMAT
from service.core.connection_manager import ConnectionManager
from service.core.task_manager import TaskManager, TaskPriority


@dataclass
class DataPoint:
    """数据点"""
    timestamp: datetime
    source: str  # 数据源标识
    module_id: Union[int, str]  # 模块标识
    parameter: str  # 参数名称
    value: Any  # 参数值
    unit: Optional[str] = None  # 单位
    status: str = "normal"  # 状态: normal, warning, error
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据


@dataclass
class CollectionResult:
    """采集结果"""
    success: bool
    data_points: List[DataPoint] = field(default_factory=list)
    error_message: Optional[str] = None
    collection_time: float = 0.0
    source: str = ""


class DataCollectorBase(ABC):
    """数据采集器基类"""
    
    def __init__(self, name: str, connection_manager: ConnectionManager):
        self.name = name
        self.connection_manager = connection_manager
        self._last_collection_time = 0.0
        self._collection_count = 0
        self._error_count = 0
    
    @abstractmethod
    async def collect_data(self, **kwargs) -> CollectionResult:
        """采集数据的抽象方法"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取采集统计信息"""
        return {
            "name": self.name,
            "collection_count": self._collection_count,
            "error_count": self._error_count,
            "last_collection_time": self._last_collection_time,
            "error_rate": self._error_count / max(self._collection_count, 1)
        }


class OCTDataCollector(DataCollectorBase):
    """OCT设备数据采集器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        super().__init__("OCT", connection_manager)
    
    async def collect_data(self, connection_id: str, 
                          slot_list: List[Union[int, str]]) -> CollectionResult:
        """采集OCT设备数据"""
        start_time = time.time()
        self._collection_count += 1
        
        try:
            # 获取连接
            device = self.connection_manager.get_connection(connection_id)
            if not device:
                raise ConnectionError(f"OCT连接不存在: {connection_id}")
            
            # 读取设备数据
            result_dict = device.read_online_display_cfp2_dco_info()
            
            data_points = []
            timestamp = datetime.now()
            
            for module_index, result_str in result_dict.items():
                # 获取SN
                sn = device.get_sn_by_result_str(result_str)
                
                # 转换结果字符串为键值对
                module_data = device.trans_result_str_to_key_value_dict(result_str)
                
                # 处理特殊字段
                if "Network FEC Uncorr Blk Cnt" in module_data:
                    module_data["Network FEC Uncorr Blk Cnt"] = int(
                        module_data["Network FEC Uncorr Blk Cnt"], 16
                    )
                
                # 创建数据点
                for param_name, param_value in module_data.items():
                    data_point = DataPoint(
                        timestamp=timestamp,
                        source="OCT",
                        module_id=module_index,
                        parameter=param_name,
                        value=param_value,
                        metadata={"sn": sn, "connection_id": connection_id}
                    )
                    data_points.append(data_point)
            
            collection_time = time.time() - start_time
            self._last_collection_time = collection_time
            
            logger.debug(f"OCT数据采集完成，采集到 {len(data_points)} 个数据点")
            
            return CollectionResult(
                success=True,
                data_points=data_points,
                collection_time=collection_time,
                source="OCT"
            )
            
        except Exception as e:
            self._error_count += 1
            error_msg = f"OCT数据采集失败: {e}"
            logger.error(error_msg)
            
            return CollectionResult(
                success=False,
                error_message=error_msg,
                collection_time=time.time() - start_time,
                source="OCT"
            )


class NPBDataCollector(DataCollectorBase):
    """NPB设备数据采集器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        super().__init__("NPB", connection_manager)
    
    async def collect_data(self, npb_connections: Dict[str, str],
                          slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]]) -> CollectionResult:
        """采集NPB设备数据"""
        start_time = time.time()
        self._collection_count += 1
        
        try:
            data_points = []
            timestamp = datetime.now()
            
            # 收集所有NPB数据
            npb_data_cache = {}
            
            for npb_ip, connection_id in npb_connections.items():
                client = self.connection_manager.get_connection(connection_id)
                if not client:
                    logger.warning(f"NPB连接不存在: {connection_id}")
                    continue
                
                try:
                    npb_data_list = client.query_npb_data()
                    for npb_doc in npb_data_list:
                        port_id = npb_doc["portid"]
                        npb_data_cache[(npb_ip, port_id)] = npb_doc
                except Exception as e:
                    logger.error(f"查询NPB数据失败 {npb_ip}: {e}")
                    continue
            
            # 按模块组织数据
            for slot, ip_port_list in slot_mapping.items():
                for npb_ip, npb_port in ip_port_list:
                    npb_doc = npb_data_cache.get((npb_ip, npb_port))
                    if not npb_doc:
                        continue
                    
                    # 创建数据点
                    for param_name, param_value in npb_doc.items():
                        if param_name in ["senderrors", "rsverrors", "sendrate", "rsvrate"]:
                            data_point = DataPoint(
                                timestamp=timestamp,
                                source="NPB",
                                module_id=slot,
                                parameter=f"{param_name}_{npb_port}",
                                value=param_value,
                                metadata={
                                    "npb_ip": npb_ip,
                                    "npb_port": npb_port,
                                    "connection_id": npb_connections[npb_ip]
                                }
                            )
                            data_points.append(data_point)
            
            collection_time = time.time() - start_time
            self._last_collection_time = collection_time
            
            logger.debug(f"NPB数据采集完成，采集到 {len(data_points)} 个数据点")
            
            return CollectionResult(
                success=True,
                data_points=data_points,
                collection_time=collection_time,
                source="NPB"
            )
            
        except Exception as e:
            self._error_count += 1
            error_msg = f"NPB数据采集失败: {e}"
            logger.error(error_msg)
            
            return CollectionResult(
                success=False,
                error_message=error_msg,
                collection_time=time.time() - start_time,
                source="NPB"
            )


class DataCollector:
    """统一数据采集器"""
    
    def __init__(self, connection_manager: ConnectionManager, task_manager: TaskManager):
        self.connection_manager = connection_manager
        self.task_manager = task_manager
        
        # 初始化各类采集器
        self.oct_collector = OCTDataCollector(connection_manager)
        self.npb_collector = NPBDataCollector(connection_manager)
        
        # 数据缓存
        self._data_cache: Dict[str, List[DataPoint]] = defaultdict(list)
        self._cache_max_size = 1000
        
        # 采集配置
        self._collection_interval = 10  # 默认10秒采集间隔
        self._enable_async_collection = True
    
    def set_collection_interval(self, interval: int) -> None:
        """设置采集间隔"""
        self._collection_interval = interval
    
    def enable_async_collection(self, enable: bool) -> None:
        """启用/禁用异步采集"""
        self._enable_async_collection = enable
    
    async def collect_oct_data(self, connection_id: str, 
                              slot_list: List[Union[int, str]]) -> CollectionResult:
        """采集OCT数据"""
        if self._enable_async_collection:
            # 异步采集
            task_id = self.task_manager.create_task(
                name=f"collect_oct_{connection_id}",
                func=self._sync_collect_oct_data,
                connection_id=connection_id,
                slot_list=slot_list,
                priority=TaskPriority.HIGH,
                timeout=30.0
            )
            
            result = self.task_manager.wait_for_task(task_id, timeout=35.0)
            if result.status.value == "completed":
                return result.result
            else:
                return CollectionResult(
                    success=False,
                    error_message=f"任务执行失败: {result.error}",
                    source="OCT"
                )
        else:
            # 同步采集
            return await self.oct_collector.collect_data(connection_id, slot_list)
    
    def _sync_collect_oct_data(self, connection_id: str, 
                              slot_list: List[Union[int, str]]) -> CollectionResult:
        """同步采集OCT数据（用于任务管理器）"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.oct_collector.collect_data(connection_id, slot_list)
            )
        finally:
            loop.close()
    
    async def collect_npb_data(self, npb_connections: Dict[str, str],
                              slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]]) -> CollectionResult:
        """采集NPB数据"""
        if self._enable_async_collection:
            # 异步采集
            task_id = self.task_manager.create_task(
                name="collect_npb_data",
                func=self._sync_collect_npb_data,
                npb_connections=npb_connections,
                slot_mapping=slot_mapping,
                priority=TaskPriority.HIGH,
                timeout=30.0
            )
            
            result = self.task_manager.wait_for_task(task_id, timeout=35.0)
            if result.status.value == "completed":
                return result.result
            else:
                return CollectionResult(
                    success=False,
                    error_message=f"任务执行失败: {result.error}",
                    source="NPB"
                )
        else:
            # 同步采集
            return await self.npb_collector.collect_data(npb_connections, slot_mapping)
    
    def _sync_collect_npb_data(self, npb_connections: Dict[str, str],
                              slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]]) -> CollectionResult:
        """同步采集NPB数据（用于任务管理器）"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.npb_collector.collect_data(npb_connections, slot_mapping)
            )
        finally:
            loop.close()
    
    async def collect_all_data(self, oct_connection_id: str, 
                              slot_list: List[Union[int, str]],
                              npb_connections: Dict[str, str],
                              slot_mapping: Dict[Union[int, str], List[Tuple[str, str]]]) -> Dict[str, CollectionResult]:
        """采集所有数据"""
        results = {}
        
        # 并发采集OCT和NPB数据
        oct_task = asyncio.create_task(
            self.collect_oct_data(oct_connection_id, slot_list)
        )
        npb_task = asyncio.create_task(
            self.collect_npb_data(npb_connections, slot_mapping)
        )
        
        # 等待所有任务完成
        oct_result, npb_result = await asyncio.gather(oct_task, npb_task)
        
        results["OCT"] = oct_result
        results["NPB"] = npb_result
        
        # 缓存数据
        if oct_result.success:
            self._cache_data("OCT", oct_result.data_points)
        
        if npb_result.success:
            self._cache_data("NPB", npb_result.data_points)
        
        return results
    
    def _cache_data(self, source: str, data_points: List[DataPoint]) -> None:
        """缓存数据"""
        cache_key = f"{source}_{datetime.now().strftime('%Y%m%d_%H')}"  # 按小时缓存
        
        self._data_cache[cache_key].extend(data_points)
        
        # 限制缓存大小
        if len(self._data_cache[cache_key]) > self._cache_max_size:
            self._data_cache[cache_key] = self._data_cache[cache_key][-self._cache_max_size:]
    
    def get_cached_data(self, source: str, hours_back: int = 1) -> List[DataPoint]:
        """获取缓存数据"""
        cached_data = []
        current_time = datetime.now()
        
        for i in range(hours_back):
            cache_time = current_time.replace(minute=0, second=0, microsecond=0)
            cache_time = cache_time.replace(hour=cache_time.hour - i)
            cache_key = f"{source}_{cache_time.strftime('%Y%m%d_%H')}"
            
            cached_data.extend(self._data_cache.get(cache_key, []))
        
        return cached_data
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取采集统计信息"""
        return {
            "oct_collector": self.oct_collector.get_statistics(),
            "npb_collector": self.npb_collector.get_statistics(),
            "cache_size": sum(len(data) for data in self._data_cache.values()),
            "cache_keys": len(self._data_cache),
            "collection_interval": self._collection_interval,
            "async_enabled": self._enable_async_collection
        }
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._data_cache.clear()
        logger.info("数据缓存已清空")
