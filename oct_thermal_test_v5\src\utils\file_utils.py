"""
File utilities module
"""

import shutil
from pathlib import Path
from typing import Union


def ensure_directory(path: Union[str, Path]) -> Path:
    """Ensure directory exists"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def backup_file(source: Union[str, Path], backup_dir: Union[str, Path]) -> Path:
    """Backup file"""
    source = Path(source)
    backup_dir = Path(backup_dir)
    ensure_directory(backup_dir)
    
    backup_path = backup_dir / source.name
    shutil.copy2(source, backup_path)
    return backup_path


def get_file_size(path: Union[str, Path]) -> int:
    """Get file size"""
    return Path(path).stat().st_size