"""
连接管理器
统一管理所有设备连接，提供连接池、健康检查和自动重连功能
"""

from __future__ import annotations
import asyncio
import threading
from typing import Dict, Optional, List, Union, Any
from dataclasses import dataclass
from enum import Enum
from contextlib import asynccontextmanager
import time

from service.common.my_logger import logger
from service.common.serial_device import SerialDevice
from service.common.ssh_device import SshDevice
from service.serial_connection.serial_connection import SerialConnection
from service.ssh.ssh import SshConnection
from service.http.npb_http import NpbHttpClinet
from service.core.config_manager import ConfigManager, ConnectionConfig


class ConnectionType(Enum):
    """连接类型枚举"""
    OCT_SSH = "oct_ssh"
    OCT_SERIAL = "oct_serial"
    NPB_HTTP = "npb_http"
    MES = "mes"


class ConnectionStatus(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    RECONNECTING = "reconnecting"


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    connection_type: ConnectionType
    host: str
    port: Union[int, str]
    status: ConnectionStatus
    last_check_time: float
    error_count: int = 0
    max_retries: int = 3
    retry_delay: int = 5
    
    def __post_init__(self):
        if self.last_check_time == 0:
            self.last_check_time = time.time()


class ConnectionPool:
    """连接池"""
    
    def __init__(self, max_size: int = 10):
        self.max_size = max_size
        self._connections: Dict[str, Any] = {}
        self._connection_info: Dict[str, ConnectionInfo] = {}
        self._lock = threading.RLock()
    
    def add_connection(self, connection_id: str, connection: Any, 
                      connection_info: ConnectionInfo) -> None:
        """添加连接到池中"""
        with self._lock:
            if len(self._connections) >= self.max_size:
                logger.warning(f"连接池已满，无法添加连接: {connection_id}")
                return
            
            self._connections[connection_id] = connection
            self._connection_info[connection_id] = connection_info
            logger.info(f"连接已添加到池中: {connection_id}")
    
    def get_connection(self, connection_id: str) -> Optional[Any]:
        """从池中获取连接"""
        with self._lock:
            return self._connections.get(connection_id)
    
    def remove_connection(self, connection_id: str) -> None:
        """从池中移除连接"""
        with self._lock:
            if connection_id in self._connections:
                connection = self._connections.pop(connection_id)
                self._connection_info.pop(connection_id, None)
                
                # 尝试关闭连接
                try:
                    if hasattr(connection, 'close'):
                        connection.close()
                    elif hasattr(connection, 'disconnect'):
                        connection.disconnect()
                except Exception as e:
                    logger.warning(f"关闭连接时出错: {e}")
                
                logger.info(f"连接已从池中移除: {connection_id}")
    
    def get_all_connections(self) -> Dict[str, Any]:
        """获取所有连接"""
        with self._lock:
            return self._connections.copy()
    
    def get_connection_info(self, connection_id: str) -> Optional[ConnectionInfo]:
        """获取连接信息"""
        with self._lock:
            return self._connection_info.get(connection_id)
    
    def update_connection_status(self, connection_id: str, 
                               status: ConnectionStatus) -> None:
        """更新连接状态"""
        with self._lock:
            if connection_id in self._connection_info:
                self._connection_info[connection_id].status = status
                self._connection_info[connection_id].last_check_time = time.time()
    
    def clear(self) -> None:
        """清空连接池"""
        with self._lock:
            for connection_id in list(self._connections.keys()):
                self.remove_connection(connection_id)


class ConnectionManager:
    """连接管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.connection_config = config_manager.get_connection_config()
        self.connection_pool = ConnectionPool(self.connection_config.connection_pool_size)
        
        self._health_check_running = False
        self._health_check_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
    
    def start_health_check(self) -> None:
        """启动健康检查"""
        if self._health_check_running:
            return
        
        self._health_check_running = True
        self._shutdown_event.clear()
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop,
            daemon=True
        )
        self._health_check_thread.start()
        logger.info("连接健康检查已启动")
    
    def stop_health_check(self) -> None:
        """停止健康检查"""
        if not self._health_check_running:
            return
        
        self._health_check_running = False
        self._shutdown_event.set()
        
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)
        
        logger.info("连接健康检查已停止")
    
    def _health_check_loop(self) -> None:
        """健康检查循环"""
        while self._health_check_running and not self._shutdown_event.is_set():
            try:
                self._perform_health_check()
                self._shutdown_event.wait(self.connection_config.keep_alive_interval)
            except Exception as e:
                logger.error(f"健康检查出错: {e}")
                self._shutdown_event.wait(10)  # 出错时等待10秒
    
    def _perform_health_check(self) -> None:
        """执行健康检查"""
        connections = self.connection_pool.get_all_connections()
        
        for connection_id, connection in connections.items():
            try:
                connection_info = self.connection_pool.get_connection_info(connection_id)
                if not connection_info:
                    continue
                
                # 检查连接是否健康
                is_healthy = self._check_connection_health(connection, connection_info)
                
                if is_healthy:
                    self.connection_pool.update_connection_status(
                        connection_id, ConnectionStatus.CONNECTED
                    )
                    connection_info.error_count = 0
                else:
                    connection_info.error_count += 1
                    if connection_info.error_count >= connection_info.max_retries:
                        logger.warning(f"连接 {connection_id} 健康检查失败次数过多，尝试重连")
                        self._reconnect_connection(connection_id)
                    else:
                        self.connection_pool.update_connection_status(
                            connection_id, ConnectionStatus.ERROR
                        )
                
            except Exception as e:
                logger.error(f"检查连接 {connection_id} 健康状态时出错: {e}")
    
    def _check_connection_health(self, connection: Any, 
                               connection_info: ConnectionInfo) -> bool:
        """检查单个连接的健康状态"""
        try:
            if connection_info.connection_type == ConnectionType.OCT_SSH:
                # SSH连接健康检查
                if hasattr(connection, 'ssh_connection'):
                    return connection.ssh_connection.connection_status
                return False
            
            elif connection_info.connection_type == ConnectionType.OCT_SERIAL:
                # 串口连接健康检查
                if hasattr(connection, 'serial_connect'):
                    return connection.serial_connect.connection_status
                return False
            
            elif connection_info.connection_type == ConnectionType.NPB_HTTP:
                # HTTP连接健康检查
                try:
                    # 简单的ping检查
                    response = connection.query_npb_data()
                    return response is not None
                except:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    def create_oct_connection(self, oct_ip: str, oct_ssh_port: int, 
                            oct_slot_list: List[Union[int, str]]) -> str:
        """创建OCT设备连接"""
        connection_id = f"oct_{oct_ip}_{oct_ssh_port}"
        
        try:
            # 获取连接配置
            oct_config = self.connection_config.oct_ssh_config.get(oct_ip, {})
            username = oct_config.get("username", self.connection_config.oct_default_username)
            password = oct_config.get("password", self.connection_config.oct_default_password)
            
            # 创建连接
            if "COM" in oct_ip:
                # 串口连接
                serial_connect = SerialConnection(oct_ip, oct_ssh_port)
                device = SerialDevice(serial_connect, oct_slot_list)
                connection_type = ConnectionType.OCT_SERIAL
            else:
                # SSH连接
                ssh_connection = SshConnection(oct_ip, oct_ssh_port, username, password)
                device = SshDevice(ssh_connection, oct_slot_list)
                connection_type = ConnectionType.OCT_SSH
            
            # 初始化连接
            device.init_device_coon()
            device.check_module_exists()
            
            # 创建连接信息
            connection_info = ConnectionInfo(
                connection_id=connection_id,
                connection_type=connection_type,
                host=oct_ip,
                port=oct_ssh_port,
                status=ConnectionStatus.CONNECTED,
                last_check_time=time.time(),
                max_retries=self.config_manager.get_config("max_retries", 3),
                retry_delay=self.config_manager.get_config("retry_delay", 5)
            )
            
            # 添加到连接池
            self.connection_pool.add_connection(connection_id, device, connection_info)
            
            logger.info(f"OCT连接创建成功: {connection_id}")
            return connection_id
            
        except Exception as e:
            logger.error(f"创建OCT连接失败: {e}")
            raise
    
    def create_npb_connections(self, npb_ip_set: set) -> Dict[str, str]:
        """创建NPB设备连接"""
        connection_ids = {}
        
        for npb_ip in npb_ip_set:
            connection_id = f"npb_{npb_ip}"
            
            try:
                # 获取连接配置
                npb_config = self.connection_config.npb_http_config.get(npb_ip, {})
                username = npb_config.get("username", self.connection_config.npb_default_username)
                password = npb_config.get("password", self.connection_config.npb_default_password)
                
                # 创建HTTP客户端
                client = NpbHttpClinet(npb_ip)
                client.login(username, password)
                
                # 创建连接信息
                connection_info = ConnectionInfo(
                    connection_id=connection_id,
                    connection_type=ConnectionType.NPB_HTTP,
                    host=npb_ip,
                    port=80,  # HTTP默认端口
                    status=ConnectionStatus.CONNECTED,
                    last_check_time=time.time()
                )
                
                # 添加到连接池
                self.connection_pool.add_connection(connection_id, client, connection_info)
                connection_ids[npb_ip] = connection_id
                
                logger.info(f"NPB连接创建成功: {connection_id}")
                
            except Exception as e:
                logger.error(f"创建NPB连接失败 {npb_ip}: {e}")
                raise
        
        return connection_ids
    
    def get_connection(self, connection_id: str) -> Optional[Any]:
        """获取连接"""
        return self.connection_pool.get_connection(connection_id)
    
    def _reconnect_connection(self, connection_id: str) -> bool:
        """重连指定连接"""
        try:
            connection_info = self.connection_pool.get_connection_info(connection_id)
            if not connection_info:
                return False
            
            logger.info(f"尝试重连: {connection_id}")
            self.connection_pool.update_connection_status(
                connection_id, ConnectionStatus.RECONNECTING
            )
            
            # 移除旧连接
            self.connection_pool.remove_connection(connection_id)
            
            # 根据连接类型重新创建连接
            # 这里需要根据实际情况实现重连逻辑
            # 暂时标记为错误状态
            connection_info.status = ConnectionStatus.ERROR
            
            return False
            
        except Exception as e:
            logger.error(f"重连失败 {connection_id}: {e}")
            return False
    
    @asynccontextmanager
    async def get_connection_async(self, connection_id: str):
        """异步获取连接的上下文管理器"""
        connection = self.get_connection(connection_id)
        if not connection:
            raise ConnectionError(f"连接不存在: {connection_id}")
        
        try:
            yield connection
        finally:
            # 这里可以添加连接使用后的清理逻辑
            pass
    
    def close_all_connections(self) -> None:
        """关闭所有连接"""
        logger.info("关闭所有连接...")
        self.stop_health_check()
        self.connection_pool.clear()
        logger.info("所有连接已关闭")
    
    def get_connection_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接状态"""
        status_info = {}
        connections = self.connection_pool.get_all_connections()
        
        for connection_id in connections:
            connection_info = self.connection_pool.get_connection_info(connection_id)
            if connection_info:
                status_info[connection_id] = {
                    "type": connection_info.connection_type.value,
                    "host": connection_info.host,
                    "port": connection_info.port,
                    "status": connection_info.status.value,
                    "last_check": connection_info.last_check_time,
                    "error_count": connection_info.error_count
                }
        
        return status_info
