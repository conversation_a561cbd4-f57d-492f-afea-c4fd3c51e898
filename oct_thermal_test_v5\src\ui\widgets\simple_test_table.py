"""
简化的测试表格组件
只显示核心数据，失败的行变红
"""

from PySide6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, 
    QAbstractItemView
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QColor, QBrush

from src.utils.logger import LoggerMixin


class SimpleTestTableWidget(QTableWidget, LoggerMixin):
    """简化的测试表格组件"""
    
    # 信号定义
    module_failed = Signal(int, str)  # 模块失败信号 (slot, reason)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._setup_table()
        
        # 数据存储
        self._test_data = {}
        self._failed_modules = set()  # 失败的模块集合
        
        # 定时器用于模拟数据更新
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._simulate_data_update)
    
    def _setup_table(self):
        """设置表格"""
        # 简化的列定义 - 只显示核心信息
        columns = [
            "插槽", "OCT设备", "NPB设备", "序列号", 
            "模块温度", "光功率", "偏置电流", 
            "发送错误", "接收错误", "状态", "最后更新"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # 表格属性设置
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setSortingEnabled(True)
        
        # 列宽设置
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置列宽
        column_widths = [60, 100, 100, 120, 100, 100, 100, 80, 80, 80, 120]
        for i, width in enumerate(column_widths):
            self.setColumnWidth(i, width)
        
        # 表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f8f8;
            }
            QTableWidget::item {
                padding: 5px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
            QHeaderView::section {
                background-color: #e0e0e0;
                padding: 8px;
                border: 1px solid #c0c0c0;
                font-weight: bold;
            }
        """)
    
    def add_test_module(self, slot: int, oct_device: str, npb_device: str):
        """添加测试模块"""
        row = self.rowCount()
        self.insertRow(row)
        
        # 设置基本信息
        items = [
            str(slot),           # 插槽
            oct_device,          # OCT设备
            npb_device,          # NPB设备
            "待获取",            # 序列号
            "0.0°C",            # 模块温度
            "0.0dBm",           # 光功率
            "0.0mA",            # 偏置电流
            "0",                # 发送错误
            "0",                # 接收错误
            "等待中",            # 状态
            "从未"               # 最后更新
        ]
        
        for col, item_text in enumerate(items):
            item = QTableWidgetItem(str(item_text))
            item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, col, item)
        
        # 设置初始行颜色（正常）
        self._set_row_color(row, QColor(255, 255, 255))  # 白色
        
        self.log_info(f"添加测试模块: 插槽 {slot}")
    
    def update_module_data(self, slot: int, data: dict):
        """更新模块数据"""
        # 查找对应的行
        row = self._find_row_by_slot(slot)
        if row == -1:
            self.log_warning(f"未找到插槽 {slot} 的行")
            return
        
        # 更新数据
        updates = {
            3: data.get("sn", "未知"),
            4: f"{data.get('temperature', 0.0):.1f}°C",
            5: f"{data.get('optical_power', 0.0):.2f}dBm",
            6: f"{data.get('bias_current', 0.0):.1f}mA",
            7: str(data.get("send_errors", 0)),
            8: str(data.get("receive_errors", 0)),
            9: data.get("status", "测试中"),
            10: data.get("last_update", "刚刚")
        }
        
        for col, value in updates.items():
            if col < self.columnCount():
                item = self.item(row, col)
                if item:
                    item.setText(str(value))
        
        # 根据状态设置行颜色
        status = data.get("status", "")
        if status == "失败" or data.get("failed", False):
            self._set_row_color(row, QColor(255, 200, 200))  # 红色背景
            self._failed_modules.add(slot)
            self.module_failed.emit(slot, data.get("failure_reason", "未知错误"))
        elif status == "成功":
            self._set_row_color(row, QColor(200, 255, 200))  # 绿色背景
            self._failed_modules.discard(slot)
        elif status == "测试中":
            self._set_row_color(row, QColor(255, 255, 200))  # 黄色背景
        else:
            self._set_row_color(row, QColor(255, 255, 255))  # 白色背景
        
        # 存储数据
        self._test_data[slot] = data
    
    def _find_row_by_slot(self, slot: int) -> int:
        """根据插槽号查找行"""
        for row in range(self.rowCount()):
            slot_item = self.item(row, 0)
            if slot_item and slot_item.text() == str(slot):
                return row
        return -1
    
    def _set_row_color(self, row: int, color: QColor):
        """设置行颜色"""
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                item.setBackground(QBrush(color))
    
    def start_simulation(self):
        """开始数据模拟（用于演示）"""
        self._update_timer.start(3000)  # 每3秒更新一次
        self.log_info("开始数据模拟")
    
    def stop_simulation(self):
        """停止数据模拟"""
        self._update_timer.stop()
        self.log_info("停止数据模拟")
    
    def _simulate_data_update(self):
        """模拟数据更新（用于演示）"""
        import random
        from datetime import datetime
        
        for row in range(self.rowCount()):
            slot_item = self.item(row, 0)
            if not slot_item:
                continue
                
            try:
                slot = int(slot_item.text())
            except ValueError:
                continue
            
            # 模拟数据
            # 10% 概率失败
            is_failed = random.random() < 0.1
            
            if is_failed:
                data = {
                    "sn": f"SN{slot:03d}ABC123",
                    "temperature": random.uniform(70.0, 85.0),  # 高温表示失败
                    "optical_power": random.uniform(-10.0, -5.0),  # 低功率表示失败
                    "bias_current": random.uniform(90.0, 120.0),  # 高电流表示失败
                    "send_errors": random.randint(10, 50),
                    "receive_errors": random.randint(5, 20),
                    "status": "失败",
                    "failed": True,
                    "failure_reason": random.choice([
                        "温度过高", "光功率异常", "偏置电流过大", 
                        "通信错误", "设备无响应", "数据校验失败"
                    ]),
                    "last_update": datetime.now().strftime("%H:%M:%S")
                }
            else:
                data = {
                    "sn": f"SN{slot:03d}ABC123",
                    "temperature": random.uniform(20.0, 45.0),
                    "optical_power": random.uniform(-3.0, 2.0),
                    "bias_current": random.uniform(30.0, 80.0),
                    "send_errors": random.randint(0, 2),
                    "receive_errors": random.randint(0, 1),
                    "status": random.choice(["测试中", "成功"]),
                    "failed": False,
                    "last_update": datetime.now().strftime("%H:%M:%S")
                }
            
            self.update_module_data(slot, data)
    
    def clear_all_data(self):
        """清空所有数据"""
        self.setRowCount(0)
        self._test_data.clear()
        self._failed_modules.clear()
        self.log_info("清空所有测试数据")
    
    def get_test_data(self) -> dict:
        """获取所有测试数据"""
        return self._test_data.copy()
    
    def get_failed_modules(self) -> set:
        """获取失败的模块列表"""
        return self._failed_modules.copy()
    
    def get_test_summary(self) -> dict:
        """获取测试摘要"""
        total_modules = self.rowCount()
        failed_modules = len(self._failed_modules)
        success_modules = total_modules - failed_modules
        
        return {
            "total": total_modules,
            "success": success_modules,
            "failed": failed_modules,
            "success_rate": (success_modules / total_modules * 100) if total_modules > 0 else 0
        }
