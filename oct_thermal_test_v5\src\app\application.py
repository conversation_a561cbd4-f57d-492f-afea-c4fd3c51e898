"""
应用程序主类
基于PySide6的应用程序架构
"""

import sys
import signal
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer, QTranslator, QLocale, qInstallMessageHandler
from PySide6.QtGui import QIcon

from config import get_settings, Settings
from src.utils.logger import setup_logger, get_logger
from src.ui.main_window import MainWindow
from src.core.test_engine import TestEngine
from src.core.device_manager import DeviceManager


class Application(QApplication):
    """
    应用程序主类
    管理应用程序的生命周期和全局状态
    """
    
    def __init__(self, argv: list[str]):
        super().__init__(argv)
        
        # 应用程序基本信息
        self.setApplicationName("OCT Thermal Test V5")
        self.setApplicationVersion("5.0.0")
        self.setOrganizationName("Your Organization")
        self.setOrganizationDomain("example.com")
        
        # 初始化组件
        self._settings: Optional[Settings] = None
        self._logger = None
        self._main_window: Optional[MainWindow] = None
        self._test_engine: Optional[TestEngine] = None
        self._device_manager: Optional[DeviceManager] = None
        self._translator: Optional[QTranslator] = None
        
        # 初始化应用程序
        self._initialize()
    
    def _initialize(self) -> None:
        """初始化应用程序"""
        try:
            # 1. 加载配置
            self._load_settings()
            
            # 2. 设置日志
            self._setup_logging()
            
            # 3. 设置国际化
            self._setup_internationalization()
            
            # 4. 设置样式和主题
            self._setup_theme()
            
            # 5. 初始化核心组件
            self._initialize_core_components()
            
            # 6. 创建主窗口
            self._create_main_window()
            
            # 7. 设置信号处理
            self._setup_signal_handlers()
            
            # 8. 设置定时器
            self._setup_timers()
            
            self._logger.info("应用程序初始化完成")
            
        except Exception as e:
            self._handle_initialization_error(e)
    
    def _load_settings(self) -> None:
        """加载应用程序设置"""
        try:
            config_path = Path("config/default.json")
            self._settings = get_settings(config_path)
            
            # 验证配置
            errors = self._settings.validate_configuration()
            if errors:
                raise ValueError(f"配置验证失败: {'; '.join(errors)}")
                
        except Exception as e:
            # 使用默认设置
            self._settings = Settings()
            print(f"警告: 无法加载配置文件，使用默认设置: {e}")
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        try:
            setup_logger(self._settings.logging)
            self._logger = get_logger(__name__)
            
            # 设置Qt消息处理器
            qInstallMessageHandler(self._qt_message_handler)
            
        except Exception as e:
            print(f"错误: 无法设置日志系统: {e}")
            # 创建基本日志记录器
            import logging
            logging.basicConfig(level=logging.INFO)
            self._logger = logging.getLogger(__name__)
    
    def _qt_message_handler(self, msg_type, context, message: str) -> None:
        """Qt消息处理器"""
        if self._logger:
            if msg_type == 0:  # QtDebugMsg
                self._logger.debug(f"Qt: {message}")
            elif msg_type == 1:  # QtWarningMsg
                self._logger.warning(f"Qt: {message}")
            elif msg_type == 2:  # QtCriticalMsg
                self._logger.error(f"Qt: {message}")
            elif msg_type == 3:  # QtFatalMsg
                self._logger.critical(f"Qt: {message}")
    
    def _setup_internationalization(self) -> None:
        """设置国际化"""
        try:
            self._translator = QTranslator()
            
            # 根据设置加载语言文件
            language = self._settings.ui.language
            if language != "en_US":
                translation_file = f"resources/translations/{language}.qm"
                if Path(translation_file).exists():
                    self._translator.load(translation_file)
                    self.installTranslator(self._translator)
                    
        except Exception as e:
            if self._logger:
                self._logger.warning(f"无法设置国际化: {e}")
    
    def _setup_theme(self) -> None:
        """设置应用程序主题"""
        try:
            theme = self._settings.ui.theme
            
            if theme == "dark":
                self._apply_dark_theme()
            elif theme == "light":
                self._apply_light_theme()
            else:  # auto
                self._apply_auto_theme()
                
        except Exception as e:
            if self._logger:
                self._logger.warning(f"无法设置主题: {e}")
    
    def _apply_dark_theme(self) -> None:
        """应用深色主题"""
        dark_style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 5px;
            border-radius: 3px;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
        QTableWidget {
            background-color: #353535;
            alternate-background-color: #404040;
            gridline-color: #555555;
        }
        QHeaderView::section {
            background-color: #404040;
            border: 1px solid #555555;
        }
        """
        self.setStyleSheet(dark_style)
    
    def _apply_light_theme(self) -> None:
        """应用浅色主题"""
        # 使用默认的浅色主题
        self.setStyleSheet("")
    
    def _apply_auto_theme(self) -> None:
        """应用自动主题（根据系统设置）"""
        # 检测系统主题并应用相应主题
        # 这里简化为浅色主题
        self._apply_light_theme()
    
    def _initialize_core_components(self) -> None:
        """初始化核心组件"""
        try:
            # 初始化设备管理器
            self._device_manager = DeviceManager(self._settings)
            
            # 初始化测试引擎
            self._test_engine = TestEngine(self._settings, self._device_manager)
            
            if self._logger:
                self._logger.info("核心组件初始化完成")
                
        except Exception as e:
            raise RuntimeError(f"核心组件初始化失败: {e}")
    
    def _create_main_window(self) -> None:
        """创建主窗口"""
        try:
            self._main_window = MainWindow(
                settings=self._settings,
                test_engine=self._test_engine,
                device_manager=self._device_manager
            )
            
            # 设置窗口图标
            icon_path = Path("resources/icons/app.ico")
            if icon_path.exists():
                self._main_window.setWindowIcon(QIcon(str(icon_path)))
            
            if self._logger:
                self._logger.info("主窗口创建完成")
                
        except Exception as e:
            raise RuntimeError(f"主窗口创建失败: {e}")
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        try:
            # 设置系统信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 连接应用程序信号
            self.aboutToQuit.connect(self._on_about_to_quit)
            
        except Exception as e:
            if self._logger:
                self._logger.warning(f"信号处理器设置失败: {e}")
    
    def _signal_handler(self, signum: int, frame) -> None:
        """系统信号处理器"""
        if self._logger:
            self._logger.info(f"接收到信号 {signum}，准备退出应用程序")
        self.quit()
    
    def _setup_timers(self) -> None:
        """设置定时器"""
        try:
            # 自动保存定时器
            if self._settings.auto_backup:
                self._backup_timer = QTimer()
                self._backup_timer.timeout.connect(self._auto_backup)
                self._backup_timer.start(self._settings.backup_interval * 1000)
                
        except Exception as e:
            if self._logger:
                self._logger.warning(f"定时器设置失败: {e}")
    
    def _auto_backup(self) -> None:
        """自动备份"""
        try:
            if self._main_window:
                self._main_window.save_layout()
            
            # 保存配置
            config_path = Path("config/user.json")
            self._settings.save_to_file(config_path)
            
            if self._logger:
                self._logger.debug("自动备份完成")
                
        except Exception as e:
            if self._logger:
                self._logger.error(f"自动备份失败: {e}")
    
    def _handle_initialization_error(self, error: Exception) -> None:
        """处理初始化错误"""
        error_msg = f"应用程序初始化失败: {error}"
        print(error_msg)
        
        # 显示错误对话框
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("初始化错误")
        msg_box.setText("应用程序初始化失败")
        msg_box.setDetailedText(str(error))
        msg_box.exec()
        
        sys.exit(1)
    
    def run(self) -> int:
        """运行应用程序"""
        try:
            if self._main_window:
                self._main_window.show()
                
                if self._logger:
                    self._logger.info("应用程序启动完成")
                
                return self.exec()
            else:
                raise RuntimeError("主窗口未创建")
                
        except Exception as e:
            if self._logger:
                self._logger.critical(f"应用程序运行失败: {e}")
            return 1
    
    def _on_about_to_quit(self) -> None:
        """应用程序即将退出时的处理"""
        try:
            if self._logger:
                self._logger.info("应用程序即将退出")
            
            # 停止测试
            if self._test_engine and self._test_engine.is_running():
                self._test_engine.stop_test()
            
            # 断开设备连接
            if self._device_manager:
                self._device_manager.disconnect_all()
            
            # 保存窗口布局
            if self._main_window:
                self._main_window.save_layout()
            
            # 保存用户配置
            if self._settings:
                config_path = Path("config/user.json")
                self._settings.save_to_file(config_path)
            
            if self._logger:
                self._logger.info("应用程序清理完成")
                
        except Exception as e:
            if self._logger:
                self._logger.error(f"应用程序清理失败: {e}")
    
    @property
    def settings(self) -> Settings:
        """获取应用程序设置"""
        return self._settings
    
    @property
    def main_window(self) -> Optional[MainWindow]:
        """获取主窗口"""
        return self._main_window
    
    @property
    def test_engine(self) -> Optional[TestEngine]:
        """获取测试引擎"""
        return self._test_engine
    
    @property
    def device_manager(self) -> Optional[DeviceManager]:
        """获取设备管理器"""
        return self._device_manager


def main() -> int:
    """应用程序入口点"""
    app = Application(sys.argv)
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
