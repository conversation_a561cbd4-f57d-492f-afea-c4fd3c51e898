"""
日志工具模块
基于loguru的日志管理
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from config.settings import LoggingConfig


# 全局日志记录器字典
_loggers: Dict[str, Any] = {}


def setup_logger(config: LoggingConfig) -> None:
    """
    设置日志系统
    
    Args:
        config: 日志配置
    """
    # 移除默认处理器
    logger.remove()
    
    # 控制台日志
    if config.console_enabled:
        logger.add(
            sys.stderr,
            level=config.level,
            format=config.format,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # 文件日志
    if config.file_enabled:
        # 确保日志目录存在
        log_path = Path(config.file_path)
        log_path.mkdir(parents=True, exist_ok=True)
        
        # 主日志文件
        logger.add(
            log_path / "app.log",
            level=config.level,
            format=config.format,
            rotation=config.file_rotation,
            retention=config.file_retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # 错误日志文件
        logger.add(
            log_path / "error.log",
            level="ERROR",
            format=config.format,
            rotation=config.file_rotation,
            retention=config.file_retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # 性能日志文件
        logger.add(
            log_path / "performance.log",
            level="INFO",
            format=config.format,
            rotation="100 MB",
            retention="7 days",
            compression="zip",
            filter=lambda record: "performance" in record["extra"]
        )


def get_logger(name: str) -> Any:
    """
    获取命名日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    if name not in _loggers:
        _loggers[name] = logger.bind(name=name)
    
    return _loggers[name]


def log_performance(func_name: str, duration: float, **kwargs) -> None:
    """
    记录性能日志
    
    Args:
        func_name: 函数名称
        duration: 执行时间（秒）
        **kwargs: 额外的性能数据
    """
    perf_logger = logger.bind(performance=True)
    perf_data = {
        "function": func_name,
        "duration": duration,
        **kwargs
    }
    perf_logger.info(f"Performance: {perf_data}")


def log_exception(exception: Exception, context: Optional[str] = None) -> None:
    """
    记录异常日志
    
    Args:
        exception: 异常对象
        context: 异常上下文信息
    """
    error_logger = get_logger("exception")
    
    if context:
        error_logger.error(f"Exception in {context}: {exception}", exc_info=True)
    else:
        error_logger.error(f"Exception: {exception}", exc_info=True)


class LoggerMixin:
    """
    日志记录器混入类
    为类提供日志记录功能
    """
    
    @property
    def logger(self):
        """获取类的日志记录器"""
        class_name = self.__class__.__name__
        return get_logger(class_name)
    
    def log_info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def log_exception(self, exception: Exception, context: Optional[str] = None) -> None:
        """记录异常日志"""
        log_exception(exception, context or self.__class__.__name__)


def performance_monitor(func):
    """
    性能监控装饰器
    自动记录函数执行时间
    """
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            log_performance(func.__name__, duration, success=True)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_performance(func.__name__, duration, success=False, error=str(e))
            raise
    
    return wrapper


def async_performance_monitor(func):
    """
    异步性能监控装饰器
    自动记录异步函数执行时间
    """
    import time
    import functools
    import asyncio
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            log_performance(func.__name__, duration, success=True, async_func=True)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_performance(func.__name__, duration, success=False, error=str(e), async_func=True)
            raise
    
    return wrapper


class ContextLogger:
    """
    上下文日志记录器
    在特定上下文中记录日志
    """
    
    def __init__(self, name: str, **context):
        self.logger = get_logger(name)
        self.context = context
    
    def __enter__(self):
        self.logger.info(f"Entering context: {self.context}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.logger.error(f"Exception in context {self.context}: {exc_val}")
        else:
            self.logger.info(f"Exiting context: {self.context}")
    
    def info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        self.logger.info(message, context=self.context, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        self.logger.warning(message, context=self.context, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        self.logger.error(message, context=self.context, **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        self.logger.debug(message, context=self.context, **kwargs)
