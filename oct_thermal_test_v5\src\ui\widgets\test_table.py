"""
测试表格组件
显示实时测试数据和状态
"""

from PySide6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, 
    QAbstractItemView, QMenu
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QColor, QBrush, QAction

from src.utils.logger import LoggerMixin


class TestTableWidget(QTableWidget, LoggerMixin):
    """测试表格组件"""
    
    # 信号定义
    row_selected = Signal(int)  # 行选择信号
    test_data_updated = Signal(dict)  # 数据更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 表格配置
        self._setup_table()
        self._setup_context_menu()
        
        # 数据存储
        self._test_data = {}
        self._row_mapping = {}  # slot -> row 映射
        
        # 定时器用于模拟数据更新
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._simulate_data_update)
    
    def _setup_table(self):
        """设置表格"""
        # 列定义
        columns = [
            "插槽", "序列号", "OCT IP", "OCT端口", "NPB IP", "NPB端口",
            "连接状态", "模块温度", "激光功率", "偏置电流", 
            "发送错误", "接收错误", "测试结果", "最后更新"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # 表格属性设置
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setSortingEnabled(True)
        
        # 列宽设置
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置列宽
        column_widths = [60, 120, 100, 80, 100, 80, 80, 100, 100, 100, 80, 80, 80, 120]
        for i, width in enumerate(column_widths):
            self.setColumnWidth(i, width)
        
        # 连接信号
        self.itemSelectionChanged.connect(self._on_selection_changed)
    
    def _setup_context_menu(self):
        """设置右键菜单"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)
    
    def _show_context_menu(self, position):
        """显示右键菜单"""
        if self.itemAt(position) is None:
            return
        
        menu = QMenu(self)
        
        # 添加菜单项
        refresh_action = QAction("刷新数据", self)
        refresh_action.triggered.connect(self._refresh_selected_row)
        menu.addAction(refresh_action)
        
        clear_errors_action = QAction("清零错误计数", self)
        clear_errors_action.triggered.connect(self._clear_errors)
        menu.addAction(clear_errors_action)
        
        menu.addSeparator()
        
        export_action = QAction("导出数据", self)
        export_action.triggered.connect(self._export_data)
        menu.addAction(export_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def add_test_module(self, slot: int, config: dict):
        """添加测试模块"""
        row = self.rowCount()
        self.insertRow(row)
        self._row_mapping[slot] = row
        
        # 设置基本信息
        items = [
            str(slot),  # 插槽
            "待获取",    # 序列号
            config.get("oct_ip", ""),
            str(config.get("oct_port", "")),
            config.get("npb_ip", ""),
            config.get("npb_port", ""),
            "未连接",    # 连接状态
            "0.0",      # 模块温度
            "0.0",      # 激光功率
            "0.0",      # 偏置电流
            "0",        # 发送错误
            "0",        # 接收错误
            "等待中",    # 测试结果
            "从未"       # 最后更新
        ]
        
        for col, item_text in enumerate(items):
            item = QTableWidgetItem(str(item_text))
            item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, col, item)
        
        # 设置初始行颜色
        self._set_row_color(row, QColor(240, 240, 240))  # 浅灰色
        
        self.log_info(f"添加测试模块: 插槽 {slot}")
    
    def update_module_data(self, slot: int, data: dict):
        """更新模块数据"""
        if slot not in self._row_mapping:
            self.log_warning(f"未找到插槽 {slot} 的行映射")
            return
        
        row = self._row_mapping[slot]
        
        # 更新数据
        updates = {
            1: data.get("sn", "未知"),
            6: data.get("connection_status", "未知"),
            7: f"{data.get('temperature', 0.0):.1f}°C",
            8: f"{data.get('optical_power', 0.0):.2f}dBm",
            9: f"{data.get('bias_current', 0.0):.1f}mA",
            10: str(data.get("send_errors", 0)),
            11: str(data.get("receive_errors", 0)),
            12: data.get("test_result", "测试中"),
            13: data.get("last_update", "刚刚")
        }
        
        for col, value in updates.items():
            if col < self.columnCount():
                item = self.item(row, col)
                if item:
                    item.setText(str(value))
        
        # 根据测试结果设置行颜色
        result = data.get("test_result", "")
        if result == "PASS":
            self._set_row_color(row, QColor(200, 255, 200))  # 浅绿色
        elif result == "FAIL":
            self._set_row_color(row, QColor(255, 200, 200))  # 浅红色
        elif result == "测试中":
            self._set_row_color(row, QColor(255, 255, 200))  # 浅黄色
        
        # 存储数据
        self._test_data[slot] = data
        
        # 发射信号
        self.test_data_updated.emit(data)
    
    def update_connection_status(self, slot: int, status: str):
        """更新连接状态"""
        if slot not in self._row_mapping:
            return
        
        row = self._row_mapping[slot]
        item = self.item(row, 6)  # 连接状态列
        if item:
            item.setText(status)
            
            # 根据状态设置颜色
            if status == "已连接":
                item.setBackground(QBrush(QColor(200, 255, 200)))
            elif status == "连接中":
                item.setBackground(QBrush(QColor(255, 255, 200)))
            else:
                item.setBackground(QBrush(QColor(255, 200, 200)))
    
    def _set_row_color(self, row: int, color: QColor):
        """设置行颜色"""
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                item.setBackground(QBrush(color))
    
    def _on_selection_changed(self):
        """选择变化处理"""
        current_row = self.currentRow()
        if current_row >= 0:
            # 查找对应的slot
            slot_item = self.item(current_row, 0)
            if slot_item:
                try:
                    slot = int(slot_item.text())
                    self.row_selected.emit(slot)
                except ValueError:
                    pass
    
    def _refresh_selected_row(self):
        """刷新选中行"""
        current_row = self.currentRow()
        if current_row >= 0:
            slot_item = self.item(current_row, 0)
            if slot_item:
                try:
                    slot = int(slot_item.text())
                    self.log_info(f"刷新插槽 {slot} 数据")
                    # 这里可以触发数据刷新
                except ValueError:
                    pass
    
    def _clear_errors(self):
        """清零错误计数"""
        current_row = self.currentRow()
        if current_row >= 0:
            # 清零发送和接收错误
            send_item = self.item(current_row, 10)
            recv_item = self.item(current_row, 11)
            if send_item:
                send_item.setText("0")
            if recv_item:
                recv_item.setText("0")
            
            self.log_info(f"清零第 {current_row + 1} 行错误计数")
    
    def _export_data(self):
        """导出数据"""
        self.log_info("导出表格数据")
        # 这里可以实现数据导出功能
    
    def start_simulation(self):
        """开始数据模拟（用于演示）"""
        self._update_timer.start(2000)  # 每2秒更新一次
    
    def stop_simulation(self):
        """停止数据模拟"""
        self._update_timer.stop()
    
    def _simulate_data_update(self):
        """模拟数据更新（用于演示）"""
        import random
        from datetime import datetime
        
        for slot in self._row_mapping.keys():
            # 模拟数据
            data = {
                "sn": f"SN{slot:03d}ABC123",
                "connection_status": "已连接",
                "temperature": random.uniform(20.0, 45.0),
                "optical_power": random.uniform(-5.0, 2.0),
                "bias_current": random.uniform(30.0, 80.0),
                "send_errors": random.randint(0, 5),
                "receive_errors": random.randint(0, 3),
                "test_result": random.choice(["PASS", "FAIL", "测试中"]),
                "last_update": datetime.now().strftime("%H:%M:%S")
            }
            
            self.update_module_data(slot, data)
    
    def clear_all_data(self):
        """清空所有数据"""
        self.setRowCount(0)
        self._test_data.clear()
        self._row_mapping.clear()
        self.log_info("清空所有测试数据")
    
    def get_test_data(self) -> dict:
        """获取所有测试数据"""
        return self._test_data.copy()
