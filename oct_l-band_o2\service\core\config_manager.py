"""
配置管理器
统一管理所有配置信息，提供配置验证和热更新功能
"""

from __future__ import annotations
import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path

from service.common.my_logger import logger


@dataclass
class TestConfig:
    """测试配置数据类"""
    oct_ip: str
    oct_ssh_port: int
    config_list: List[Dict[str, Any]]
    single_loop_time: int
    test_start_time: str
    
    # 超时配置
    stability_check_timeout: int = 300  # 5分钟
    stability_check_interval: int = 10  # 10秒
    post_stability_wait_time: int = 120  # 2分钟
    
    # 并发配置
    max_workers: int = 4
    task_timeout: int = 60
    
    # 重试配置
    max_retries: int = 3
    retry_delay: int = 5


@dataclass
class ConnectionConfig:
    """连接配置数据类"""
    npb_http_config: Dict[str, Dict[str, str]] = field(default_factory=dict)
    oct_ssh_config: Dict[str, Dict[str, str]] = field(default_factory=dict)
    
    # 默认连接参数
    npb_default_username: str = "admin"
    npb_default_password: str = "Admin_123"
    oct_default_username: str = "admin"
    oct_default_password: str = "Admin_123"
    
    # 连接池配置
    connection_pool_size: int = 10
    connection_timeout: int = 30
    keep_alive_interval: int = 60


@dataclass
class ValidationConfig:
    """验证配置数据类"""
    control_value_list: List[Dict[str, Any]] = field(default_factory=list)
    output_key_list: List[str] = field(default_factory=list)
    error_key_list: List[str] = field(default_factory=list)
    
    # 验证规则
    enable_dynamic_threshold: bool = True
    threshold_update_interval: int = 100  # 每100次测量更新一次阈值


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "./config.json"
        self._config_cache: Dict[str, Any] = {}
        self._test_config: Optional[TestConfig] = None
        self._connection_config: Optional[ConnectionConfig] = None
        self._validation_config: Optional[ValidationConfig] = None
        
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"配置文件不存在: {self.config_path}, 使用默认配置")
                self._create_default_config()
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config_cache = json.load(f)
            
            self._parse_config()
            logger.info(f"配置加载成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """创建默认配置"""
        default_config = {
            "app_ver": "1.0.0",
            "log_dir": "logs",
            "mes": 0,
            "single_loop_time": 10,
            "npb_http_config": {},
            "oct_ssh_config": {},
            "max_workers": 4,
            "task_timeout": 60,
            "stability_check_timeout": 300,
            "stability_check_interval": 10,
            "post_stability_wait_time": 120
        }
        
        self._config_cache = default_config
        self.save_config()
        self._parse_config()
    
    def _parse_config(self) -> None:
        """解析配置到数据类"""
        # 解析连接配置
        self._connection_config = ConnectionConfig(
            npb_http_config=self._config_cache.get("npb_http_config", {}),
            oct_ssh_config=self._config_cache.get("oct_ssh_config", {}),
            connection_pool_size=self._config_cache.get("connection_pool_size", 10),
            connection_timeout=self._config_cache.get("connection_timeout", 30),
            keep_alive_interval=self._config_cache.get("keep_alive_interval", 60)
        )
        
        # 解析验证配置
        from service.common.const import CONTROL_VALUE_LIST, OUTPUT_KEY_LIST, ERROR_KEY_LISY
        self._validation_config = ValidationConfig(
            control_value_list=CONTROL_VALUE_LIST.copy(),
            output_key_list=OUTPUT_KEY_LIST.copy(),
            error_key_list=ERROR_KEY_LISY.copy(),
            enable_dynamic_threshold=self._config_cache.get("enable_dynamic_threshold", True),
            threshold_update_interval=self._config_cache.get("threshold_update_interval", 100)
        )
    
    def get_test_config(self, oct_ip: str, oct_ssh_port: int, 
                       config_list: List[Dict], test_start_time: str) -> TestConfig:
        """获取测试配置"""
        if not self._test_config:
            self._test_config = TestConfig(
                oct_ip=oct_ip,
                oct_ssh_port=oct_ssh_port,
                config_list=config_list,
                single_loop_time=self._config_cache.get("single_loop_time", 10),
                test_start_time=test_start_time,
                stability_check_timeout=self._config_cache.get("stability_check_timeout", 300),
                stability_check_interval=self._config_cache.get("stability_check_interval", 10),
                post_stability_wait_time=self._config_cache.get("post_stability_wait_time", 120),
                max_workers=self._config_cache.get("max_workers", 4),
                task_timeout=self._config_cache.get("task_timeout", 60),
                max_retries=self._config_cache.get("max_retries", 3),
                retry_delay=self._config_cache.get("retry_delay", 5)
            )
        return self._test_config
    
    def get_connection_config(self) -> ConnectionConfig:
        """获取连接配置"""
        return self._connection_config
    
    def get_validation_config(self) -> ValidationConfig:
        """获取验证配置"""
        return self._validation_config
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self._config_cache.get(key, default)
    
    def set_config(self, key: str, value: Any) -> None:
        """设置配置项"""
        self._config_cache[key] = value
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config_cache, f, ensure_ascii=False, indent=4)
            logger.info(f"配置保存成功: {self.config_path}")
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证必要的配置项
            required_keys = ["single_loop_time", "max_workers"]
            for key in required_keys:
                if key not in self._config_cache:
                    logger.error(f"缺少必要配置项: {key}")
                    return False
            
            # 验证数值范围
            if self._config_cache.get("single_loop_time", 0) <= 0:
                logger.error("single_loop_time 必须大于0")
                return False
            
            if self._config_cache.get("max_workers", 0) <= 0:
                logger.error("max_workers 必须大于0")
                return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def reload_config(self) -> None:
        """重新加载配置"""
        logger.info("重新加载配置...")
        self.load_config()
    
    def get_output_path(self) -> str:
        """获取输出路径"""
        from service.common.const import BASE_OUTPUT_FILE_PATH
        return BASE_OUTPUT_FILE_PATH
    
    def get_log_dir(self) -> str:
        """获取日志目录"""
        return self._config_cache.get("log_dir", "logs")
