"""
任务管理器
提供多任务并发处理、任务调度和状态管理功能
"""

from __future__ import annotations
import asyncio
import threading
import queue
import time
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import Thread<PERSON>oolExecutor, Future
import uuid

from service.common.my_logger import logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Task:
    """任务数据类"""
    task_id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: Optional[float] = None
    max_retries: int = 0
    retry_delay: float = 1.0
    
    # 运行时状态
    status: TaskStatus = TaskStatus.PENDING
    created_time: float = field(default_factory=time.time)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Any = None
    error: Optional[Exception] = None
    retry_count: int = 0
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        if not isinstance(other, Task):
            return NotImplemented
        return self.priority.value > other.priority.value  # 高优先级在前


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    execution_time: float = 0.0
    retry_count: int = 0


class TaskQueue:
    """任务队列"""
    
    def __init__(self, maxsize: int = 0):
        self._queue = queue.PriorityQueue(maxsize=maxsize)
        self._task_count = 0
    
    def put(self, task: Task, block: bool = True, timeout: Optional[float] = None) -> None:
        """添加任务到队列"""
        # 使用计数器确保相同优先级的任务按FIFO顺序
        priority_item = (task.priority.value, self._task_count, task)
        self._queue.put(priority_item, block=block, timeout=timeout)
        self._task_count += 1
    
    def get(self, block: bool = True, timeout: Optional[float] = None) -> Task:
        """从队列获取任务"""
        _, _, task = self._queue.get(block=block, timeout=timeout)
        return task
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()


class WorkerPool:
    """工作池"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self._active_tasks: Dict[str, Future] = {}
        self._lock = threading.RLock()
    
    def submit_task(self, task: Task) -> Future:
        """提交任务到工作池"""
        with self._lock:
            future = self.executor.submit(self._execute_task, task)
            self._active_tasks[task.task_id] = future
            return future
    
    def _execute_task(self, task: Task) -> TaskResult:
        """执行任务"""
        task.status = TaskStatus.RUNNING
        task.start_time = time.time()
        
        try:
            # 执行任务函数
            if task.timeout:
                # 带超时的执行
                result = self._execute_with_timeout(task)
            else:
                result = task.func(*task.args, **task.kwargs)
            
            task.result = result
            task.status = TaskStatus.COMPLETED
            
            return TaskResult(
                task_id=task.task_id,
                status=TaskStatus.COMPLETED,
                result=result,
                execution_time=time.time() - task.start_time,
                retry_count=task.retry_count
            )
            
        except Exception as e:
            task.error = e
            task.status = TaskStatus.FAILED
            
            return TaskResult(
                task_id=task.task_id,
                status=TaskStatus.FAILED,
                error=e,
                execution_time=time.time() - task.start_time,
                retry_count=task.retry_count
            )
        
        finally:
            task.end_time = time.time()
            with self._lock:
                self._active_tasks.pop(task.task_id, None)
    
    def _execute_with_timeout(self, task: Task) -> Any:
        """带超时的任务执行"""
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError(f"任务 {task.task_id} 执行超时")
        
        # 设置超时信号
        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(int(task.timeout))
        
        try:
            result = task.func(*task.args, **task.kwargs)
            signal.alarm(0)  # 取消超时
            return result
        finally:
            signal.signal(signal.SIGALRM, old_handler)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            future = self._active_tasks.get(task_id)
            if future:
                return future.cancel()
            return False
    
    def get_active_tasks(self) -> List[str]:
        """获取活跃任务列表"""
        with self._lock:
            return list(self._active_tasks.keys())
    
    def shutdown(self, wait: bool = True) -> None:
        """关闭工作池"""
        self.executor.shutdown(wait=wait)


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, max_workers: int = 4):
        self.task_queue = TaskQueue()
        self.worker_pool = WorkerPool(max_workers)
        self._tasks: Dict[str, Task] = {}
        self._task_results: Dict[str, TaskResult] = {}
        self._running = False
        self._scheduler_thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
    
    def start(self) -> None:
        """启动调度器"""
        if self._running:
            return
        
        self._running = True
        self._scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            daemon=True
        )
        self._scheduler_thread.start()
        logger.info("任务调度器已启动")
    
    def stop(self) -> None:
        """停止调度器"""
        if not self._running:
            return
        
        self._running = False
        
        # 等待调度器线程结束
        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=5)
        
        # 关闭工作池
        self.worker_pool.shutdown(wait=True)
        
        logger.info("任务调度器已停止")
    
    def _scheduler_loop(self) -> None:
        """调度器主循环"""
        while self._running:
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=1.0)
                
                # 提交任务到工作池
                future = self.worker_pool.submit_task(task)
                
                # 设置回调处理任务结果
                future.add_done_callback(
                    lambda f, t=task: self._handle_task_completion(t, f)
                )
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"调度器循环出错: {e}")
    
    def _handle_task_completion(self, task: Task, future: Future) -> None:
        """处理任务完成"""
        try:
            result = future.result()
            
            with self._lock:
                self._task_results[task.task_id] = result
            
            # 如果任务失败且还有重试次数，重新提交任务
            if (result.status == TaskStatus.FAILED and 
                task.retry_count < task.max_retries):
                
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                
                logger.info(f"任务 {task.task_id} 失败，进行第 {task.retry_count} 次重试")
                
                # 延迟后重新提交
                threading.Timer(
                    task.retry_delay,
                    lambda: self.task_queue.put(task)
                ).start()
            
            logger.info(f"任务 {task.task_id} 完成，状态: {result.status.value}")
            
        except Exception as e:
            logger.error(f"处理任务完成时出错: {e}")
    
    def submit_task(self, task: Task) -> str:
        """提交任务"""
        with self._lock:
            self._tasks[task.task_id] = task
        
        self.task_queue.put(task)
        logger.info(f"任务已提交: {task.task_id} ({task.name})")
        
        return task.task_id
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        with self._lock:
            return self._task_results.get(task_id)
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        with self._lock:
            task = self._tasks.get(task_id)
            return task.status if task else None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        # 尝试从工作池取消
        if self.worker_pool.cancel_task(task_id):
            with self._lock:
                task = self._tasks.get(task_id)
                if task:
                    task.status = TaskStatus.CANCELLED
            return True
        
        return False
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.task_queue.qsize()
    
    def get_active_tasks(self) -> List[str]:
        """获取活跃任务"""
        return self.worker_pool.get_active_tasks()


class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.scheduler = TaskScheduler(max_workers)
        self._event_handlers: Dict[str, List[Callable]] = {}
    
    def start(self) -> None:
        """启动任务管理器"""
        self.scheduler.start()
        logger.info("任务管理器已启动")
    
    def stop(self) -> None:
        """停止任务管理器"""
        self.scheduler.stop()
        logger.info("任务管理器已停止")
    
    def create_task(self, name: str, func: Callable, *args, 
                   priority: TaskPriority = TaskPriority.NORMAL,
                   timeout: Optional[float] = None,
                   max_retries: int = 0,
                   retry_delay: float = 1.0,
                   **kwargs) -> str:
        """创建并提交任务"""
        task = Task(
            task_id=str(uuid.uuid4()),
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            max_retries=max_retries,
            retry_delay=retry_delay
        )
        
        return self.scheduler.submit_task(task)
    
    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> TaskResult:
        """等待任务完成"""
        start_time = time.time()
        
        while True:
            result = self.scheduler.get_task_result(task_id)
            if result:
                return result
            
            if timeout and (time.time() - start_time) > timeout:
                raise TimeoutError(f"等待任务 {task_id} 超时")
            
            time.sleep(0.1)
    
    def wait_for_all_tasks(self, task_ids: List[str], 
                          timeout: Optional[float] = None) -> Dict[str, TaskResult]:
        """等待多个任务完成"""
        results = {}
        start_time = time.time()
        
        while len(results) < len(task_ids):
            for task_id in task_ids:
                if task_id not in results:
                    result = self.scheduler.get_task_result(task_id)
                    if result:
                        results[task_id] = result
            
            if timeout and (time.time() - start_time) > timeout:
                raise TimeoutError("等待任务组超时")
            
            time.sleep(0.1)
        
        return results
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        return self.scheduler.get_task_status(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return self.scheduler.cancel_task(task_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            "queue_size": self.scheduler.get_queue_size(),
            "active_tasks": len(self.scheduler.get_active_tasks()),
            "max_workers": self.scheduler.worker_pool.max_workers
        }
    
    def add_event_handler(self, event_type: str, handler: Callable) -> None:
        """添加事件处理器"""
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)
    
    def emit_event(self, event_type: str, *args, **kwargs) -> None:
        """触发事件"""
        handlers = self._event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                handler(*args, **kwargs)
            except Exception as e:
                logger.error(f"事件处理器出错 {event_type}: {e}")
