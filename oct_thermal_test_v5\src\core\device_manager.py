"""
Device manager module
"""

from config.settings import Settings
from src.utils.logger import LoggerMixin


class DeviceManager(LoggerMixin):
    """Device manager"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._devices = {}
    
    def connect_all(self):
        """Connect all devices"""
        self.log_info("Connecting all devices")
    
    def disconnect_all(self):
        """Disconnect all devices"""
        self.log_info("Disconnecting all devices")