"""
核心组件模块
提供测试系统的核心组件实现
"""

__version__ = "1.0.0"
__author__ = "System Architect"

# 导出核心组件
from .test_orchestrator import TestOrchestrator
from .config_manager import ConfigManager
from .connection_manager import ConnectionManager
from .data_collector import DataCollector
from .validation_engine import ValidationEngine
from .report_generator import ReportGenerator
from .task_manager import TaskManager

__all__ = [
    "TestOrchestrator",
    "ConfigManager", 
    "ConnectionManager",
    "DataCollector",
    "ValidationEngine",
    "ReportGenerator",
    "TaskManager"
]
