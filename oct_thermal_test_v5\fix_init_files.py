#!/usr/bin/env python3
"""
修复__init__.py文件
"""

import os
from pathlib import Path

def fix_init_files():
    """修复所有__init__.py文件"""
    
    init_files = {
        'src/__init__.py': '''"""
OCT Thermal Test V5 source code package
"""

__version__ = "5.0.0"
__author__ = "Development Team"
__email__ = "<EMAIL>"
''',
        
        'src/app/__init__.py': '''"""
Application core module
"""

from .application import Application
from .constants import *

__all__ = ["Application"]
''',
        
        'src/core/__init__.py': '''"""
Core business logic module
"""
''',
        
        'src/devices/__init__.py': '''"""
Device interfaces module
"""
''',
        
        'src/models/__init__.py': '''"""
Data models module
"""
''',
        
        'src/ui/__init__.py': '''"""
User interface module
"""
''',
        
        'src/ui/widgets/__init__.py': '''"""
UI widgets module
"""
''',
        
        'src/ui/dialogs/__init__.py': '''"""
UI dialogs module
"""
''',
        
        'src/utils/__init__.py': '''"""
Utilities module
"""

from .logger import setup_logger, get_logger
from .async_utils import AsyncTaskManager, run_async_task
from .file_utils import ensure_directory, backup_file, get_file_size

__all__ = [
    "setup_logger",
    "get_logger", 
    "AsyncTaskManager",
    "run_async_task",
    "ensure_directory",
    "backup_file",
    "get_file_size"
]
''',
        
        'src/workers/__init__.py': '''"""
Background workers module
"""
''',
        
        'tests/__init__.py': '''"""
Tests module
"""
''',
        
        'tests/test_core/__init__.py': '''"""
Core tests module
"""
''',
        
        'tests/test_devices/__init__.py': '''"""
Device tests module
"""
''',
        
        'tests/test_ui/__init__.py': '''"""
UI tests module
"""
''',
        
        'tests/test_models/__init__.py': '''"""
Model tests module
"""
'''
    }
    
    for file_path, content in init_files.items():
        full_path = Path(file_path)
        
        # 删除现有文件
        if full_path.exists():
            full_path.unlink()
            print(f"Deleted: {file_path}")
        
        # 确保目录存在
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建新文件
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        
        print(f"Created: {file_path}")
    
    print("All __init__.py files fixed!")

if __name__ == "__main__":
    fix_init_files()
