"""
验证引擎
提供数据验证、规则检查和阈值管理功能
"""

from __future__ import annotations
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod
from enum import Enum

from service.common.my_logger import logger
from service.core.data_collector import DataPoint
from service.core.config_manager import ConfigManager, ValidationConfig


class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    SKIP = "skip"


@dataclass
class ValidationRule:
    """验证规则"""
    rule_id: str
    name: str
    parameter: str
    rule_type: str  # "range", "increase", "custom"
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    value_type: str = "float"  # "int", "float", "string"
    enabled: bool = True
    
    # 特殊规则配置
    sn_specific: bool = False  # 是否针对特定SN
    target_sn: Optional[str] = None
    
    # 动态阈值配置
    dynamic_threshold: bool = False
    threshold_update_count: int = 0
    
    # 元数据
    unit: Optional[str] = None
    description: Optional[str] = None
    created_time: float = field(default_factory=time.time)


@dataclass
class ValidationError:
    """验证错误"""
    timestamp: datetime
    module_id: Union[int, str]
    parameter: str
    value: Any
    expected_min: Optional[float]
    expected_max: Optional[float]
    rule_id: str
    error_type: str
    message: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationReport:
    """验证报告"""
    timestamp: datetime
    module_id: Union[int, str]
    total_checks: int
    passed_checks: int
    failed_checks: int
    warning_checks: int
    skipped_checks: int
    errors: List[ValidationError] = field(default_factory=list)
    is_pass: bool = True
    
    def __post_init__(self):
        self.is_pass = self.failed_checks == 0


class ValidatorBase(ABC):
    """验证器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self._validation_count = 0
        self._error_count = 0
    
    @abstractmethod
    def validate(self, data_point: DataPoint, rule: ValidationRule) -> Tuple[ValidationResult, Optional[str]]:
        """验证数据点"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取验证统计"""
        return {
            "name": self.name,
            "validation_count": self._validation_count,
            "error_count": self._error_count,
            "error_rate": self._error_count / max(self._validation_count, 1)
        }


class RangeValidator(ValidatorBase):
    """范围验证器"""
    
    def __init__(self):
        super().__init__("RangeValidator")
    
    def validate(self, data_point: DataPoint, rule: ValidationRule) -> Tuple[ValidationResult, Optional[str]]:
        """验证数值范围"""
        self._validation_count += 1
        
        try:
            # 类型转换
            if rule.value_type == "int":
                value = int(data_point.value)
            elif rule.value_type == "float":
                value = float(data_point.value)
            else:
                return ValidationResult.SKIP, "不支持的数值类型"
            
            # 范围检查
            if rule.min_value is not None and value < rule.min_value:
                self._error_count += 1
                return ValidationResult.FAIL, f"值 {value} 小于最小值 {rule.min_value}"
            
            if rule.max_value is not None and value > rule.max_value:
                self._error_count += 1
                return ValidationResult.FAIL, f"值 {value} 大于最大值 {rule.max_value}"
            
            return ValidationResult.PASS, None
            
        except (ValueError, TypeError) as e:
            self._error_count += 1
            return ValidationResult.FAIL, f"数值转换失败: {e}"


class IncreaseValidator(ValidatorBase):
    """递增验证器"""
    
    def __init__(self):
        super().__init__("IncreaseValidator")
        self._baseline_values: Dict[str, float] = {}
    
    def validate(self, data_point: DataPoint, rule: ValidationRule) -> Tuple[ValidationResult, Optional[str]]:
        """验证数值是否保持不变（用于错误计数等）"""
        self._validation_count += 1
        
        try:
            # 类型转换
            if rule.value_type == "int":
                value = int(data_point.value)
            elif rule.value_type == "float":
                value = float(data_point.value)
            else:
                return ValidationResult.SKIP, "不支持的数值类型"
            
            # 构建基线键
            baseline_key = f"{data_point.module_id}_{rule.parameter}"
            if rule.sn_specific and rule.target_sn:
                baseline_key += f"_{rule.target_sn}"
            
            # 检查是否需要设置基线
            if baseline_key not in self._baseline_values:
                if rule.min_value == -999 and rule.max_value == -999:
                    # 首次设置基线值
                    self._baseline_values[baseline_key] = value
                    return ValidationResult.PASS, f"设置基线值: {value}"
                else:
                    self._baseline_values[baseline_key] = rule.min_value or 0
            
            # 检查是否针对特定SN
            if rule.sn_specific and rule.target_sn:
                sn = data_point.metadata.get("sn")
                if sn != rule.target_sn:
                    return ValidationResult.SKIP, f"SN不匹配: {sn} != {rule.target_sn}"
            
            # 验证值是否保持不变
            baseline_value = self._baseline_values[baseline_key]
            if value != baseline_value:
                self._error_count += 1
                return ValidationResult.FAIL, f"值发生变化: {value} != {baseline_value}"
            
            return ValidationResult.PASS, None
            
        except (ValueError, TypeError) as e:
            self._error_count += 1
            return ValidationResult.FAIL, f"数值转换失败: {e}"
    
    def update_baseline(self, module_id: Union[int, str], parameter: str, 
                       value: float, sn: Optional[str] = None) -> None:
        """更新基线值"""
        baseline_key = f"{module_id}_{parameter}"
        if sn:
            baseline_key += f"_{sn}"
        
        self._baseline_values[baseline_key] = value
        logger.info(f"更新基线值: {baseline_key} = {value}")


class ThresholdValidator(ValidatorBase):
    """阈值验证器（支持动态阈值）"""
    
    def __init__(self):
        super().__init__("ThresholdValidator")
        self._value_history: Dict[str, List[float]] = {}
        self._dynamic_thresholds: Dict[str, Tuple[float, float]] = {}
    
    def validate(self, data_point: DataPoint, rule: ValidationRule) -> Tuple[ValidationResult, Optional[str]]:
        """验证阈值"""
        self._validation_count += 1
        
        try:
            # 类型转换
            if rule.value_type == "int":
                value = int(data_point.value)
            elif rule.value_type == "float":
                value = float(data_point.value)
            else:
                return ValidationResult.SKIP, "不支持的数值类型"
            
            # 动态阈值处理
            if rule.dynamic_threshold:
                threshold_key = f"{data_point.module_id}_{rule.parameter}"
                self._update_dynamic_threshold(threshold_key, value, rule)
                
                # 使用动态阈值
                if threshold_key in self._dynamic_thresholds:
                    min_val, max_val = self._dynamic_thresholds[threshold_key]
                else:
                    min_val, max_val = rule.min_value, rule.max_value
            else:
                min_val, max_val = rule.min_value, rule.max_value
            
            # 阈值检查
            if min_val is not None and value < min_val:
                self._error_count += 1
                return ValidationResult.FAIL, f"值 {value} 低于阈值 {min_val}"
            
            if max_val is not None and value > max_val:
                self._error_count += 1
                return ValidationResult.FAIL, f"值 {value} 超过阈值 {max_val}"
            
            return ValidationResult.PASS, None
            
        except (ValueError, TypeError) as e:
            self._error_count += 1
            return ValidationResult.FAIL, f"数值转换失败: {e}"
    
    def _update_dynamic_threshold(self, threshold_key: str, value: float, rule: ValidationRule) -> None:
        """更新动态阈值"""
        if threshold_key not in self._value_history:
            self._value_history[threshold_key] = []
        
        self._value_history[threshold_key].append(value)
        
        # 保持历史数据在合理范围内
        max_history = 100
        if len(self._value_history[threshold_key]) > max_history:
            self._value_history[threshold_key] = self._value_history[threshold_key][-max_history:]
        
        # 计算动态阈值（使用统计方法）
        values = self._value_history[threshold_key]
        if len(values) >= 10:  # 至少10个数据点才计算动态阈值
            mean_val = sum(values) / len(values)
            variance = sum((x - mean_val) ** 2 for x in values) / len(values)
            std_dev = variance ** 0.5
            
            # 使用3σ原则设置动态阈值
            dynamic_min = mean_val - 3 * std_dev
            dynamic_max = mean_val + 3 * std_dev
            
            self._dynamic_thresholds[threshold_key] = (dynamic_min, dynamic_max)


class RuleEngine:
    """规则引擎"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.validation_config = config_manager.get_validation_config()
        
        # 初始化验证器
        self.validators = {
            "range": RangeValidator(),
            "increase": IncreaseValidator(),
            "threshold": ThresholdValidator()
        }
        
        # 规则管理
        self._rules: Dict[str, ValidationRule] = {}
        self._load_rules()
    
    def _load_rules(self) -> None:
        """加载验证规则"""
        control_value_list = self.validation_config.control_value_list
        
        for i, control_dict in enumerate(control_value_list):
            rule_id = f"rule_{i}"
            rule_type = "increase" if control_dict.get("type") == "Increase" else "range"
            
            rule = ValidationRule(
                rule_id=rule_id,
                name=control_dict["name"],
                parameter=control_dict["name"],
                rule_type=rule_type,
                min_value=control_dict.get("min_value"),
                max_value=control_dict.get("max_value"),
                value_type=control_dict.get("value_type", "float"),
                sn_specific=control_dict.get("type") == "Increase",
                target_sn=control_dict.get("sn"),
                dynamic_threshold=self.validation_config.enable_dynamic_threshold
            )
            
            self._rules[rule_id] = rule
        
        logger.info(f"加载了 {len(self._rules)} 个验证规则")
    
    def add_rule(self, rule: ValidationRule) -> None:
        """添加验证规则"""
        self._rules[rule.rule_id] = rule
        logger.info(f"添加验证规则: {rule.rule_id}")
    
    def remove_rule(self, rule_id: str) -> None:
        """移除验证规则"""
        if rule_id in self._rules:
            del self._rules[rule_id]
            logger.info(f"移除验证规则: {rule_id}")
    
    def get_rules_for_parameter(self, parameter: str) -> List[ValidationRule]:
        """获取参数的验证规则"""
        return [rule for rule in self._rules.values() 
                if rule.parameter == parameter and rule.enabled]
    
    def validate_data_point(self, data_point: DataPoint) -> List[Tuple[ValidationRule, ValidationResult, Optional[str]]]:
        """验证单个数据点"""
        results = []
        rules = self.get_rules_for_parameter(data_point.parameter)
        
        for rule in rules:
            validator = self.validators.get(rule.rule_type)
            if not validator:
                logger.warning(f"未找到验证器: {rule.rule_type}")
                continue
            
            result, message = validator.validate(data_point, rule)
            results.append((rule, result, message))
        
        return results


class ValidationEngine:
    """验证引擎"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.rule_engine = RuleEngine(config_manager)
        
        # 验证统计
        self._validation_history: List[ValidationReport] = []
        self._max_history = 1000
    
    def validate_module_data(self, module_id: Union[int, str], 
                           data_points: List[DataPoint]) -> ValidationReport:
        """验证模块数据"""
        timestamp = datetime.now()
        errors = []
        
        total_checks = 0
        passed_checks = 0
        failed_checks = 0
        warning_checks = 0
        skipped_checks = 0
        
        for data_point in data_points:
            if data_point.module_id != module_id:
                continue
            
            validation_results = self.rule_engine.validate_data_point(data_point)
            
            for rule, result, message in validation_results:
                total_checks += 1
                
                if result == ValidationResult.PASS:
                    passed_checks += 1
                elif result == ValidationResult.FAIL:
                    failed_checks += 1
                    
                    # 创建验证错误
                    error = ValidationError(
                        timestamp=timestamp,
                        module_id=module_id,
                        parameter=data_point.parameter,
                        value=data_point.value,
                        expected_min=rule.min_value,
                        expected_max=rule.max_value,
                        rule_id=rule.rule_id,
                        error_type=rule.rule_type,
                        message=message or "验证失败",
                        metadata=data_point.metadata
                    )
                    errors.append(error)
                    
                elif result == ValidationResult.WARNING:
                    warning_checks += 1
                elif result == ValidationResult.SKIP:
                    skipped_checks += 1
        
        # 创建验证报告
        report = ValidationReport(
            timestamp=timestamp,
            module_id=module_id,
            total_checks=total_checks,
            passed_checks=passed_checks,
            failed_checks=failed_checks,
            warning_checks=warning_checks,
            skipped_checks=skipped_checks,
            errors=errors
        )
        
        # 保存到历史记录
        self._validation_history.append(report)
        if len(self._validation_history) > self._max_history:
            self._validation_history = self._validation_history[-self._max_history:]
        
        return report
    
    def validate_all_modules(self, all_data_points: List[DataPoint]) -> Dict[Union[int, str], ValidationReport]:
        """验证所有模块数据"""
        # 按模块分组数据
        module_data = {}
        for data_point in all_data_points:
            if data_point.module_id not in module_data:
                module_data[data_point.module_id] = []
            module_data[data_point.module_id].append(data_point)
        
        # 验证每个模块
        reports = {}
        for module_id, data_points in module_data.items():
            reports[module_id] = self.validate_module_data(module_id, data_points)
        
        return reports
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        if not self._validation_history:
            return {"total_validations": 0}
        
        recent_reports = self._validation_history[-100:]  # 最近100次验证
        
        total_validations = len(recent_reports)
        total_checks = sum(r.total_checks for r in recent_reports)
        total_passed = sum(r.passed_checks for r in recent_reports)
        total_failed = sum(r.failed_checks for r in recent_reports)
        
        validator_stats = {}
        for name, validator in self.rule_engine.validators.items():
            validator_stats[name] = validator.get_statistics()
        
        return {
            "total_validations": total_validations,
            "total_checks": total_checks,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "pass_rate": total_passed / max(total_checks, 1),
            "validator_statistics": validator_stats,
            "active_rules": len(self.rule_engine._rules)
        }
    
    def get_recent_errors(self, count: int = 50) -> List[ValidationError]:
        """获取最近的验证错误"""
        all_errors = []
        for report in reversed(self._validation_history):
            all_errors.extend(report.errors)
            if len(all_errors) >= count:
                break
        
        return all_errors[:count]
    
    def update_rule_threshold(self, rule_id: str, min_value: Optional[float], 
                            max_value: Optional[float]) -> bool:
        """更新规则阈值"""
        if rule_id in self.rule_engine._rules:
            rule = self.rule_engine._rules[rule_id]
            rule.min_value = min_value
            rule.max_value = max_value
            logger.info(f"更新规则阈值: {rule_id}, min={min_value}, max={max_value}")
            return True
        return False
