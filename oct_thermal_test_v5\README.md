# OCT L-Band O2 温循测试上位机 V5.0

## 项目简介

这是一个基于PySide6开发的OCT L-Band O2温循测试上位机系统，用于自动化测试光通信设备的温度循环性能。

## 主要功能

- **设备连接管理**: 支持OCT设备的SSH/串口连接，NPB设备的HTTP连接
- **稳定性检查**: 自动检测设备连接稳定性，确保测试环境就绪
- **数据采集**: 实时采集设备运行数据，支持多模块并发采集
- **数据验证**: 基于可配置规则的实时数据验证和阈值检查
- **报告生成**: 自动生成Excel格式的测试报告
- **MES集成**: 与制造执行系统集成，支持工序管理
- **实时监控**: 图形化界面实时显示测试状态和数据

## 技术特性

- **现代化架构**: 基于PySide6的MVP架构设计
- **异步处理**: 使用QThread和asyncio实现高效的并发处理
- **模块化设计**: 清晰的分层架构，易于维护和扩展
- **配置驱动**: 支持JSON配置文件，灵活的参数配置
- **完善的日志**: 结构化日志记录，便于问题诊断
- **单元测试**: 完整的测试覆盖，确保代码质量

## 系统要求

- Python 3.10+
- PySide6 6.5+
- Windows 10/11 (主要支持平台)
- 8GB+ RAM
- 网络连接 (用于设备通信)

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行应用

```bash
python main.py
```

### 配置设备

1. 编辑 `config/default.json` 文件
2. 配置OCT设备连接信息
3. 配置NPB设备连接信息
4. 设置测试参数

## 项目结构

```
oct_thermal_test_v5/
├── src/                    # 源代码
│   ├── app/               # 应用核心
│   ├── ui/                # 用户界面
│   ├── core/              # 业务逻辑
│   ├── devices/           # 设备接口
│   ├── models/            # 数据模型
│   ├── utils/             # 工具类
│   └── workers/           # 后台线程
├── config/                # 配置文件
├── tests/                 # 测试代码
├── docs/                  # 文档
└── resources/             # 资源文件
```

## 开发指南

### 代码规范

- 遵循PEP 8代码风格
- 使用类型注解
- 编写文档字符串
- 保持函数简洁

### 架构原则

- **单一职责**: 每个类只负责一个功能
- **依赖注入**: 通过构造函数注入依赖
- **接口隔离**: 定义清晰的接口边界
- **开闭原则**: 对扩展开放，对修改关闭

### 测试策略

- 单元测试覆盖核心逻辑
- 集成测试验证设备交互
- UI测试确保界面功能
- 性能测试验证系统性能

## 配置说明

### 设备配置

```json
{
  "devices": {
    "oct": {
      "connection_type": "ssh",
      "host": "*************",
      "port": 22,
      "username": "admin",
      "password": "Admin_123"
    },
    "npb": {
      "hosts": ["*************", "*************"],
      "username": "admin",
      "password": "Admin_123"
    }
  }
}
```

### 测试配置

```json
{
  "test": {
    "loop_interval": 10,
    "stability_timeout": 300,
    "max_workers": 4,
    "validation_rules": [
      {
        "parameter": "Module Temperature",
        "min_value": 0,
        "max_value": 70,
        "unit": "°C"
      }
    ]
  }
}
```

## API文档

详细的API文档请参考 [docs/api.md](docs/api.md)

## 更新日志

### V5.0.0 (2025-01-XX)

- 🎉 全新的PySide6架构设计
- ⚡ 异步并发处理，提升性能
- 🔧 模块化设计，易于维护
- 📊 增强的数据验证和报告功能
- 🐛 修复了V4.0中的已知问题

### V4.0.0

- 基础功能实现
- OCT和NPB设备支持
- Excel报告生成

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

## 致谢

感谢所有为这个项目做出贡献的开发者和测试人员。
