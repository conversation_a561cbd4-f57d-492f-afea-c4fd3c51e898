"""
应用程序常量定义
"""

from enum import Enum
from typing import Dict, List

# 应用程序信息
APP_NAME = "OCT Thermal Test V5"
APP_VERSION = "5.0.0"
APP_ORGANIZATION = "Your Organization"
APP_DOMAIN = "example.com"

# 文件和路径
DEFAULT_CONFIG_FILE = "config/default.json"
USER_CONFIG_FILE = "config/user.json"
LOG_DIR = "logs"
OUTPUT_DIR = "output"
TEMP_DIR = "temp"

# 时间格式
STANDARD_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
EXCEL_NAME_TIME_FORMAT = "%Y%m%d_%H%M%S"
LOG_TIME_FORMAT = "%Y-%m-%d %H:%M:%S.%f"

# 测试状态
class TestStatus(Enum):
    """测试状态枚举"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    CONNECTING = "connecting"
    STABILITY_CHECK = "stability_check"
    CLEARING_ERRORS = "clearing_errors"
    TESTING = "testing"
    GENERATING_REPORTS = "generating_reports"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class DeviceStatus(Enum):
    """设备状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    RECONNECTING = "reconnecting"
    TIMEOUT = "timeout"


class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    SKIP = "skip"
    ERROR = "error"


# UI相关常量
class UIConstants:
    """UI常量"""
    # 窗口尺寸
    MIN_WINDOW_WIDTH = 800
    MIN_WINDOW_HEIGHT = 600
    DEFAULT_WINDOW_WIDTH = 1200
    DEFAULT_WINDOW_HEIGHT = 800
    
    # 表格列宽
    TABLE_COLUMN_WIDTHS = {
        "slot": 60,
        "sn": 120,
        "ip": 120,
        "port": 80,
        "npb_ip": 120,
        "npb_data_port": 100,
        "status": 80,
        "temperature": 100,
        "power": 100,
        "current": 100,
        "errors": 150,
        "result": 80
    }
    
    # 颜色定义
    COLORS = {
        "success": "#4CAF50",
        "warning": "#FF9800", 
        "error": "#F44336",
        "info": "#2196F3",
        "default": "#9E9E9E",
        "background": "#FAFAFA",
        "surface": "#FFFFFF",
        "primary": "#1976D2",
        "secondary": "#424242"
    }
    
    # 图标
    ICONS = {
        "start": "▶",
        "stop": "⏹",
        "pause": "⏸",
        "refresh": "🔄",
        "settings": "⚙",
        "export": "📤",
        "import": "📥",
        "save": "💾",
        "open": "📁",
        "close": "❌",
        "success": "✅",
        "error": "❌",
        "warning": "⚠",
        "info": "ℹ"
    }


# 设备相关常量
class DeviceConstants:
    """设备常量"""
    # 默认连接参数
    DEFAULT_SSH_PORT = 22
    DEFAULT_HTTP_PORT = 80
    DEFAULT_TIMEOUT = 30
    DEFAULT_RETRY_COUNT = 3
    DEFAULT_RETRY_DELAY = 1.0
    
    # OCT设备
    OCT_DEFAULT_USERNAME = "admin"
    OCT_DEFAULT_PASSWORD = "Admin_123"
    OCT_MAX_SLOTS = 8
    
    # NPB设备
    NPB_DEFAULT_USERNAME = "admin"
    NPB_DEFAULT_PASSWORD = "Admin_123"
    NPB_MAX_PORTS = 48
    
    # 连接池配置
    CONNECTION_POOL_SIZE = 10
    CONNECTION_KEEP_ALIVE = 60
    CONNECTION_HEALTH_CHECK_INTERVAL = 30


# 测试相关常量
class TestConstants:
    """测试常量"""
    # 默认测试参数
    DEFAULT_LOOP_INTERVAL = 10  # 秒
    DEFAULT_STABILITY_TIMEOUT = 300  # 5分钟
    DEFAULT_STABILITY_CHECK_INTERVAL = 10  # 秒
    DEFAULT_POST_STABILITY_WAIT = 120  # 2分钟
    
    # 任务管理
    DEFAULT_MAX_WORKERS = 4
    DEFAULT_TASK_TIMEOUT = 60
    DEFAULT_MAX_RETRIES = 3
    
    # 数据验证
    TEMPERATURE_MIN = -40  # °C
    TEMPERATURE_MAX = 85   # °C
    POWER_MIN = -30        # dBm
    POWER_MAX = 10         # dBm
    CURRENT_MIN = 0        # mA
    CURRENT_MAX = 200      # mA


# 输出相关常量
OUTPUT_KEY_LIST = [
    "datetime",
    "Module Temperature",
    "Laser Bias Current", 
    "Optical Power",
    "Network FEC Uncorr Blk Cnt",
    "npb_send_errors",
    "npb_receive_errors",
    "is_this_time_pass"
]

ERROR_KEY_LIST = [
    "datetime",
    "name",
    "value", 
    "min_value",
    "max_value",
    "is_pass"
]

# 结果映射
RESULT_STR_MAPPER = {
    True: "PASS",
    False: "FAIL"
}

# 验证规则模板
DEFAULT_VALIDATION_RULES = [
    {
        "name": "Module Temperature",
        "parameter": "Module Temperature",
        "min_value": 0,
        "max_value": 70,
        "value_type": "float",
        "rule_type": "range",
        "unit": "°C"
    },
    {
        "name": "Laser Bias Current",
        "parameter": "Laser Bias Current", 
        "min_value": 0,
        "max_value": 100,
        "value_type": "float",
        "rule_type": "range",
        "unit": "mA"
    },
    {
        "name": "Optical Power",
        "parameter": "Optical Power",
        "min_value": -10,
        "max_value": 5,
        "value_type": "float", 
        "rule_type": "range",
        "unit": "dBm"
    },
    {
        "name": "Network FEC Uncorr Blk Cnt",
        "parameter": "Network FEC Uncorr Blk Cnt",
        "min_value": -999,
        "max_value": -999,
        "value_type": "int",
        "rule_type": "increase",
        "sn_specific": True
    }
]

# 错误代码
class ErrorCodes:
    """错误代码定义"""
    # 通用错误
    UNKNOWN_ERROR = 1000
    CONFIGURATION_ERROR = 1001
    VALIDATION_ERROR = 1002
    
    # 连接错误
    CONNECTION_FAILED = 2000
    CONNECTION_TIMEOUT = 2001
    CONNECTION_LOST = 2002
    AUTHENTICATION_FAILED = 2003
    
    # 设备错误
    DEVICE_NOT_FOUND = 3000
    DEVICE_NOT_RESPONDING = 3001
    DEVICE_ERROR = 3002
    DEVICE_BUSY = 3003
    
    # 测试错误
    TEST_FAILED = 4000
    TEST_TIMEOUT = 4001
    TEST_CANCELLED = 4002
    VALIDATION_FAILED = 4003
    
    # 文件错误
    FILE_NOT_FOUND = 5000
    FILE_ACCESS_ERROR = 5001
    FILE_FORMAT_ERROR = 5002
    DISK_FULL = 5003


# 消息类型
class MessageType(Enum):
    """消息类型枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"
    DEBUG = "debug"


# 信号名称
class SignalNames:
    """信号名称常量"""
    # 测试相关信号
    TEST_STARTED = "test_started"
    TEST_STOPPED = "test_stopped"
    TEST_PAUSED = "test_paused"
    TEST_RESUMED = "test_resumed"
    TEST_COMPLETED = "test_completed"
    TEST_FAILED = "test_failed"
    
    # 设备相关信号
    DEVICE_CONNECTED = "device_connected"
    DEVICE_DISCONNECTED = "device_disconnected"
    DEVICE_ERROR = "device_error"
    
    # 数据相关信号
    DATA_RECEIVED = "data_received"
    DATA_VALIDATED = "data_validated"
    REPORT_GENERATED = "report_generated"
    
    # UI相关信号
    STATUS_UPDATED = "status_updated"
    PROGRESS_UPDATED = "progress_updated"
    MESSAGE_POSTED = "message_posted"
    TABLE_UPDATED = "table_updated"
