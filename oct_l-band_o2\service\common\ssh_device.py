from typing import List, Dict, Union
import re

from service.ssh.ssh import SshConnection
from service.common.my_logger import logger
from service.common.const import SINGLE_VALUE_KEY_LIST, MULTI_VALUE_KEY_LIST
from utils.module_data import extract_cfp2_module_data


class SshDevice:
    def __init__(
        self, ssh_connection: SshConnection, oct_slot_list: list[Union[int, str]]
    ):
        self.ssh_connection = ssh_connection
        self.module_info_dict: Dict[Union[int, str], Dict] = {
            module_index: {"online_status": False, "module_index": module_index}
            for module_index in oct_slot_list
        }

    def init_device_coon(self):
        if self.ssh_connection.connection_status == False:
            self.ssh_connection.close()
        self.ssh_connection.connect()
        result_sys = self.ssh_connection.send_command("sys")
        logger.info(f"连接ssh成功: {result_sys}")

        # if result_sys == "sys \r\n[ACCELINK]":
        #     logger.info(
        #         f"连接ssh成功, ip: {self.ssh_connection.ip}, port: {self.ssh_connection.port}"
        #     )
        # else:
        #     raise ConnectionError(
        #         f"连接ssh失败, ip: {self.ssh_connection.ip}, port: {self.ssh_connection.port}"
        #     )

    def switch_module(self, slot_str: str):
        logger.info(f"开始切换槽位到：{slot_str}")
        slot_command = f"slot {slot_str}"
        result_slot: str = self.ssh_connection.send_command(slot_command)
        logger.info(f"切换查询模块: {result_slot}")

    def ssh_send_exit(self):
        self.ssh_connection.send_command("exit")

    def check_module_exists(self):
        for module_index, module_info in self.module_info_dict.items():
            slot_str = module_index[:-2]
            module_number = module_index[-1:]
            self.switch_module(slot_str)
            result_display: str = self.send_display_cfp2_dco_command()
            if "The cfp2dco Module No.1 module is absent!" in result_display:
                module_info.update({"online_status": False})
                raise Exception(f"插槽{slot_str}上按照配置应该有模块,但是通过OCT设备获取到的数据显示该插槽上没有模块")
            elif result_display:
                if f"CFP2_DCO Module No.{module_number} information" in result_display:
                    module_info.update({"online_status": True})
            else:
                self.ssh_send_exit()
                raise ConnectionError(
                    f"设备接受命令失败, 返回值为空字符串, ip: {self.ssh_connection.ip}, port: {self.ssh_connection.port}"
                )
            self.ssh_send_exit()

    def format_slot_str(self, slot_index: Union[int, str]):
        if type(slot_index) is int:
            return f"1/{slot_index}"
        else:
            return f"1/{slot_index}"

    def read_online_display_cfp2_dco_info(self):
        result_dict = {}
        for module_index, module_info in self.module_info_dict.items():
            online_status: bool = module_info["online_status"]
            if online_status:
                result_display = self.read_single_display_cfp2_dco_info(module_index)
                module_result_display = extract_cfp2_module_data(result_display, int(module_index[-1:]))
                result_dict.update({module_index: module_result_display})
        return result_dict

    def send_display_cfp2_dco_command(self):
        result_display: str = self.ssh_connection.send_command("display cfp2-dco")
        return result_display

    def read_single_display_cfp2_dco_info(self, module_index: Union[int, str]):
        
        slot_str = module_index[:-2]
        self.switch_module(slot_str)
        module_number = int(module_index[-1:])
        result_display = self.send_display_cfp2_dco_command()
        module_result_display = extract_cfp2_module_data(result_display, module_number)
        logger.info(f"read_single_display_cfp2_dco_info:{module_result_display}")
        self.ssh_send_exit()
        return result_display

    def trans_result_str_to_key_value_dict(self, result_str: str) -> Dict[str, str]:
        key_list = SINGLE_VALUE_KEY_LIST
        key_str = "|".join(key_list)
        pattern = re.compile(rf"({key_str})\s*:\s*(\S+)")
        matches = pattern.findall(result_str)
        # 将结果转换为字典
        result = {key: value for key, value in matches}
        key_list_multi = MULTI_VALUE_KEY_LIST
        key_str_multi = "|".join(key_list_multi)
        pattern_multi = re.compile(rf"({key_str_multi})\s*:\s*(.*?)\s*(?=\n|$)")
        matches_multi = pattern_multi.findall(result_str)
        result_multi: Dict[str, str] = {key: value for key, value in matches_multi}
        for k, v in result_multi.items():
            values = v.split(" ")
            for value_index, value in enumerate(values):
                result.update({f"{k}[{value_index + 1}]": value})
        return result

    def get_sn_by_result_str(self, result_str: str) -> str:
        pattern = re.compile(r"Vendor SN\s*:\s*(\S+)")
        match = pattern.search(result_str)
        if match:
            result = match.group(1)
            return result
        else:
            raise Exception("无法从字符串中获取到Vendor SN")

if __name__ == '__main__':
    test = """
    display cfp2-dco
There are 2 CFP2_DCO module online:

   CFP2_DCO Module No.1 information:

    Gene Info: 
      Tx Laser Mode                 :	OFF
      Power Mode                    :	Normal Power
      Key Status                    :	Rx-LOS Laser-Off 
      MdDev Mode Value              :	0x01
      MdDev KeyStatus Value         :	0x14
      MdDev Voltage Value           :	3380 mV
      MdDev Current Value           :	5191 mA
      MdDev Power Value             :	17545 mW

    Manu Info: 
      Vendor Name                   :	Accelink        
      Vendor PN                     :	OCT-04C2-I4LN   
      Vendor SN                     :	L3              
      Vendor DateCode               :	20250407

    Basic Info: 
      Connector Type                :	LC
      Ethernet App Code             :	Undefined
      Host Lane Number              :	8
      Network Lane Number           :	1
      Device Technology             :	DBR,N/A
      Transceiver Temperature Th.   :	0.00 C, 70.00 C
      VCC Alarm Th.                 :	3.22 V, 3.46 V
      Tx Power Alarm Th.            :	0.00 dBm, 0.00 dBm
      Rx Power Alarm Th.            :	-21.00 dBm, 1.00 dBm
      Tx Min Laser Freq             :	186087.50 GHz
      Rx Min Laser Freq             :	186087.50 GHz
      Tx Rx Max Laser Freq          :	190850.00 GHz
      Laser Tuning Capability       :	6.25 GHz 12.5 GHz 25 GHz 50 GHz 100 GHz 

    Real Info: 
      Module State                  :	0x0008
      General Status Latch          :	0x0076
      Alarm and Warning Latch       :	0x0000
      Module Temperature            :	40.00 C
      Module Power Supply           :	3.37 V
      SOA Bias                      :	0.00 mA
      Tx Laser Bias                 :	296.10 mA
      Network Alm Warning 1 Latch   :	0x0003
      Network Alm Warning 2 Latch   :	0x0000
      Network Fault Status Latch    :	0x0018
      TX Channel Ctrl               :	6.25 GHz grid spacing, Channel number:739
      Network TX Laser Output       :	17.00 dBm
      Network TX Laser Temp         :	38.44 C
      Desired TX Output             :	0.00 dBm
      TX Current Freq               :	190700.00 GHz
      RX Current Freq               :	190700.00 GHz
      Current Output Power          :	-40.00 dBm
      Current Input Power           :	-40.00 dBm
      Network Rx Outage Duration    :	0 us
      General Mode Control MSB1     :	0x0059
      General Mode Control MSB2     :	0x0f02
      Network Current BER           :	1.000000e+00
      Network FEC Uncorr Blk Cnt    :	0x00000000
      Network FEC Uncorr Blk Reset  :	0
      Network FEC Uncorr Blk Rate   :	1.000000e+00
      Network Cur FEC Uncorr Bc Rate:	1.000000e+00
      Network Ave FEC Uncorr Bc Rate:	1.000000e+00
      Network Min FEC Uncorr Bc Rate:	1.000000e+00
      Network Max FEC Uncorr Bc Rate:	1.000000e+00
      Network Loopback Control      :	0x0000
      Client Tx FEC Uncorr Blk SMR  :	0 0 0 0
      Client Tx FEC Corr Bit SMR    :	0.000000 0.000000 0.000000 0.000000
      Client Tx Post FEC Ber PM-Int :	1.000000e+00 1.000000e+00 1.000000e+00 1.000000e+00
      Client Tx FEC Ber over PM-Int :	1.000000e+00 1.000000e+00 1.000000e+00 1.000000e+00
      
      Type            CD(ps/nm)       DGD(ps)    SOPMD(ps^2) SOP(rad/s) PDL(dB)    Q(dB)      CFO(MHz)   SNR        OSNR       
      Current         0               0          0          0           0.0        0.0        0          0.0        0.0        
      Avg             0               0          0          0           0.0        0.0        0          0.0        0.0        
      Max             0               0          0          0           0.0        0.0        0          0.0        0.0        
      Min             0               0          0          0           0.0        0.0        0          0.0        0.0        

   CFP2_DCO Module No.2 information:

    Gene Info: 
      Tx Laser Mode                 :	OFF
      Power Mode                    :	Low Power
      Key Status                    :	Rx-LOS Laser-Off 
      MdDev Mode Value              :	0x0d
      MdDev KeyStatus Value         :	0x14
      MdDev Voltage Value           :	3384 mV
      MdDev Current Value           :	382 mA
      MdDev Power Value             :	1292 mW

    Manu Info: 
      Vendor Name                   :	Accelink        
      Vendor PN                     :	OCT-04C2-I4LN   
      Vendor SN                     :	L2              
      Vendor DateCode               :	20250317

    Basic Info: 
      Connector Type                :	LC
      Ethernet App Code             :	Undefined
      Host Lane Number              :	8
      Network Lane Number           :	1
      Device Technology             :	DBR,N/A
      Transceiver Temperature Th.   :	0.00 C, 70.00 C
      VCC Alarm Th.                 :	3.22 V, 3.46 V
      Tx Power Alarm Th.            :	0.00 dBm, 0.00 dBm
      Rx Power Alarm Th.            :	-21.00 dBm, 1.00 dBm
      Tx Min Laser Freq             :	186087.50 GHz
      Rx Min Laser Freq             :	186087.50 GHz
      Tx Rx Max Laser Freq          :	190850.00 GHz
      Laser Tuning Capability       :	6.25 GHz 12.5 GHz 25 GHz 50 GHz 100 GHz 

    Real Info: 
      Module State                  :	0x0002
      General Status Latch          :	0x0024
      Alarm and Warning Latch       :	0x0000
      Module Temperature            :	25.00 C
      Module Power Supply           :	3.42 V
      SOA Bias                      :	0.00 mA
      Tx Laser Bias                 :	0.00 mA
      Network Alm Warning 1 Latch   :	0x0000
      Network Alm Warning 2 Latch   :	0x0000
      Network Fault Status Latch    :	0x0010
      TX Channel Ctrl               :	6.25 GHz grid spacing, Channel number:739
      Network TX Laser Output       :	-40.00 dBm
      Network TX Laser Temp         :	0.00 C
      Desired TX Output             :	0.00 dBm
      TX Current Freq               :	0.00 GHz
      RX Current Freq               :	N/A GHz
      Current Output Power          :	-39.99 dBm
      Current Input Power           :	-39.99 dBm
      Network Rx Outage Duration    :	0 us
      General Mode Control MSB1     :	0x0059
      General Mode Control MSB2     :	0x0f02
      Network Current BER           :	1.000000e+00
      Network FEC Uncorr Blk Cnt    :	0x00000000
      Network FEC Uncorr Blk Reset  :	0
      Network FEC Uncorr Blk Rate   :	1.000000e+00
      Network Cur FEC Uncorr Bc Rate:	1.000000e+00
      Network Ave FEC Uncorr Bc Rate:	1.000000e+00
      Network Min FEC Uncorr Bc Rate:	1.000000e+00
      Network Max FEC Uncorr Bc Rate:	1.000000e+00
      Network Loopback Control      :	0x0000
      Client Tx FEC Uncorr Blk SMR  :	0 0 0 0
      Client Tx FEC Corr Bit SMR    :	0.000000 0.000000 0.000000 0.000000
      Client Tx Post FEC Ber PM-Int :	0.000000e+00 0.000000e+00 0.000000e+00 0.000000e+00
      Client Tx FEC Ber over PM-Int :	0.000000e+00 0.000000e+00 0.000000e+00 0.000000e+00
      
      Type            CD(ps/nm)       DGD(ps)    SOPMD(ps^2) SOP(rad/s) PDL(dB)    Q(dB)      CFO(MHz)   SNR        OSNR       
      Current         0               0          0          0           0.0        0.0        0          0.0        0.0        
      Avg             0               0          0          0           0.0        0.0        0          0.0        0.0        
      Max             0               0          0          0           0.0        0.0        0          0.0        0.0        
      Min             0               0          0          0           0.0        0.0        0          0.0        0.0        
    """
    ssh_device = SshDevice(
            SshConnection("172.16.166.210", 22, "admin", "Admin_123"), ['1/1/1', '1/1/2', '1/2/1', '1/2/2', '1/3/1', '1/3/2', '1/4/1']
    )
    ssh_device.init_device_coon()
    ssh_device.check_module_exists()