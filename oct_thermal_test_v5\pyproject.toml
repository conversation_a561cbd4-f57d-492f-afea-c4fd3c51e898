[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "oct-thermal-test"
version = "5.0.0"
description = "OCT L-Band O2 温循测试上位机系统"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Development Team", email = "<EMAIL>"}
]
keywords = ["oct", "thermal", "test", "pyside6", "automation"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Manufacturing",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Electronic Design Automation (EDA)",
    "Topic :: System :: Hardware :: Hardware Drivers",
]
requires-python = ">=3.10"
dependencies = [
    "PySide6>=6.5.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "requests>=2.31.0",
    "paramiko>=3.3.0",
    "pyserial>=3.5",
    "pydantic>=2.0.0",
    "loguru>=0.7.0",
    "asyncio-mqtt>=0.13.0",
    "aiofiles>=23.0.0",
    "typing-extensions>=4.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-qt>=4.2.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-qt>=4.2.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/oct-thermal-test"
Documentation = "https://oct-thermal-test.readthedocs.io"
Repository = "https://github.com/your-org/oct-thermal-test.git"
Issues = "https://github.com/your-org/oct-thermal-test/issues"
Changelog = "https://github.com/your-org/oct-thermal-test/blob/main/CHANGELOG.md"

[project.scripts]
oct-thermal-test = "src.app.application:main"

[project.gui-scripts]
oct-thermal-test-gui = "main:main"

[tool.setuptools.packages.find]
where = ["src"]
include = ["src*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
"src.ui.resources" = ["*.qrc", "*.ui", "*.png", "*.ico", "*.svg"]
"config" = ["*.json", "*.yaml", "*.yml"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["PySide6", "pandas", "openpyxl", "requests", "paramiko"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "paramiko.*",
    "serial.*",
    "openpyxl.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "ui: marks tests as UI tests",
    "device: marks tests that require real devices",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".env",
]
