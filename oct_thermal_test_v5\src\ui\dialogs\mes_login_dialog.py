"""
MES登录对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QLabel, QCheckBox,
    QMessageBox, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QPixmap

from config.settings import Settings
from src.utils.logger import LoggerMixin


class MESLoginDialog(QDialog, LoggerMixin):
    """MES登录对话框"""
    
    login_success = Signal(str)  # 登录成功信号，传递用户名
    
    def __init__(self, settings: Settings, parent=None):
        super().__init__(parent)
        
        self.settings = settings
        self._setup_ui()
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("MES系统登录")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("MES系统登录")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                padding: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # 登录表单
        form_layout = QFormLayout()
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入MES用户名")
        self.username_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        
        self.server_edit = QLineEdit()
        self.server_edit.setText(self.settings.mes.server_url)
        self.server_edit.setPlaceholderText("MES服务器地址")
        self.server_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        
        form_layout.addRow("用户名:", self.username_edit)
        form_layout.addRow("密码:", self.password_edit)
        form_layout.addRow("服务器:", self.server_edit)
        
        layout.addLayout(form_layout)
        
        # 记住密码选项
        self.remember_check = QCheckBox("记住密码")
        layout.addWidget(self.remember_check)
        
        # 进度条（登录时显示）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.login_btn = QPushButton("登录")
        self.login_btn.setMinimumHeight(40)
        self.login_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.login_btn.clicked.connect(self._login)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setMinimumHeight(40)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 连接回车键
        self.username_edit.returnPressed.connect(self._login)
        self.password_edit.returnPressed.connect(self._login)
        
        # 加载保存的用户名
        self._load_saved_credentials()
    
    def _load_saved_credentials(self):
        """加载保存的凭据"""
        # 这里可以从配置文件或注册表加载保存的用户名
        # 为了安全，不保存密码
        pass
    
    def _login(self):
        """执行登录"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        server = self.server_edit.text().strip()
        
        # 验证输入
        if not username:
            self.status_label.setText("请输入用户名")
            self.username_edit.setFocus()
            return
        
        if not password:
            self.status_label.setText("请输入密码")
            self.password_edit.setFocus()
            return
        
        if not server:
            self.status_label.setText("请输入服务器地址")
            self.server_edit.setFocus()
            return
        
        # 开始登录过程
        self._start_login_process(username, password, server)
    
    def _start_login_process(self, username: str, password: str, server: str):
        """开始登录过程"""
        self.login_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.status_label.setText("正在连接MES系统...")
        
        # 模拟登录过程
        self._simulate_login(username, password, server)
    
    def _simulate_login(self, username: str, password: str, server: str):
        """模拟登录过程"""
        # 这里应该实现真实的MES登录逻辑
        # 现在只是模拟
        
        def login_step_1():
            self.status_label.setText("验证用户凭据...")
            self.progress_bar.setValue(30)
            QTimer.singleShot(1000, login_step_2)
        
        def login_step_2():
            self.status_label.setText("获取用户权限...")
            self.progress_bar.setValue(60)
            QTimer.singleShot(1000, login_step_3)
        
        def login_step_3():
            self.status_label.setText("初始化会话...")
            self.progress_bar.setValue(90)
            QTimer.singleShot(1000, login_complete)
        
        def login_complete():
            # 模拟登录结果
            import random
            success = random.choice([True, True, True, False])  # 75%成功率
            
            if success:
                self.progress_bar.setValue(100)
                self.status_label.setText("登录成功！")
                self.status_label.setStyleSheet("color: green; font-size: 12px;")
                
                # 保存凭据（如果选择了记住密码）
                if self.remember_check.isChecked():
                    self._save_credentials(username)
                
                # 发射登录成功信号
                self.login_success.emit(username)
                
                # 延迟关闭对话框
                QTimer.singleShot(1000, self.accept)
                
                self.log_info(f"MES登录成功: {username}")
                
            else:
                self.progress_bar.setVisible(False)
                self.status_label.setText("登录失败：用户名或密码错误")
                self.status_label.setStyleSheet("color: red; font-size: 12px;")
                self.login_btn.setEnabled(True)
                
                self.log_warning(f"MES登录失败: {username}")
        
        # 开始登录流程
        self.progress_bar.setValue(10)
        QTimer.singleShot(500, login_step_1)
    
    def _save_credentials(self, username: str):
        """保存凭据"""
        # 这里可以将用户名保存到配置文件
        # 为了安全，不保存密码
        self.log_info(f"保存MES用户凭据: {username}")
    
    def get_login_info(self) -> dict:
        """获取登录信息"""
        return {
            "username": self.username_edit.text().strip(),
            "server": self.server_edit.text().strip(),
            "remember": self.remember_check.isChecked()
        }
